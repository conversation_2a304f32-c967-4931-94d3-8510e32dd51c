<template>
  <view class="app-container">
    <router-view></router-view>
  </view>
</template>

<script setup lang="ts">
  import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

  // App 生命周期
  onLaunch(() => {
    console.log('App Launch')
  })

  onShow(() => {
    console.log('App Show')
  })

  onHide(() => {
    console.log('App Hide')
  })
</script>

<style lang="scss">
  /* 引入全局样式 */
  @import '@/styles/index.scss';
  @import 'uview-plus/index.scss';

  .app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
</style>
