<template>
  <view class="page-container">
    <u-navbar title="排课系统" :border="false" />

    <!-- 统计卡片 -->
    <view class="stats-container">
      <view class="stat-card" @click="navigateTo('/pages/teacher/index')">
        <text class="stat-number">{{ teacherCount }}</text>
        <text class="stat-label">教师数量</text>
      </view>
      <view class="stat-card" @click="navigateTo('/pages/student/index')">
        <text class="stat-number">{{ studentCount }}</text>
        <text class="stat-label">学生数量</text>
      </view>
      <view class="stat-card" @click="navigateTo('/pages/group/index')">
        <text class="stat-number">{{ groupCount }}</text>
        <text class="stat-label">组数量</text>
      </view>
      <view class="stat-card" @click="navigateTo('/pages/task/index')">
        <text class="stat-number">{{ taskCount }}</text>
        <text class="stat-label">排课任务</text>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="section-title">快捷操作</view>
      <view class="action-grid">
        <u-grid :border="false" :col="2">
          <u-grid-item @click="navigateTo('/pages/task/add')">
            <u-icon name="plus-circle" size="28" color="#2979ff" />
            <text class="grid-text">新建排课</text>
          </u-grid-item>
          <u-grid-item @click="navigateTo('/pages/teacher/add')">
            <u-icon name="account" size="28" color="#2979ff" />
            <text class="grid-text">添加教师</text>
          </u-grid-item>
          <u-grid-item @click="navigateTo('/pages/student/add')">
            <u-icon name="account-fill" size="28" color="#2979ff" />
            <text class="grid-text">添加学生</text>
          </u-grid-item>
          <u-grid-item @click="navigateTo('/pages/group/add')">
            <u-icon name="grid" size="28" color="#2979ff" />
            <text class="grid-text">新建组</text>
          </u-grid-item>
        </u-grid>
      </view>
    </view>

    <!-- 最近任务 -->
    <view v-if="recentTasks.length" class="recent-tasks">
      <view class="section-title">最近任务</view>
      <view class="task-list">
        <view
          v-for="task in recentTasks"
          :key="task.id"
          class="task-card"
          @click="navigateTo(`/pages/task/detail?id=${task.id}`)"
        >
          <view class="task-header">
            <text class="task-name">{{ task.name }}</text>
            <u-tag :text="task.status" :type="getStatusType(task.status)" size="mini" />
          </view>
          <view class="task-time">创建时间：{{ formatDate(task.createdAt) }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useTeacherStore, useStudentStore, useGroupStore, useTaskStore } from '@/store'
  import { formatDate } from '@/utils/date'

  const teacherStore = useTeacherStore()
  const studentStore = useStudentStore()
  const groupStore = useGroupStore()
  const taskStore = useTaskStore()

  // 统计数据
  const teacherCount = ref(0)
  const studentCount = ref(0)
  const groupCount = ref(0)
  const taskCount = ref(0)
  const recentTasks = ref([])

  // 获取数据
  onMounted(async () => {
    await Promise.all([
      teacherStore.fetchTeachers(),
      studentStore.fetchStudents(),
      groupStore.fetchGroups(),
      taskStore.fetchTasks()
    ])

    teacherCount.value = teacherStore.teachers.length
    studentCount.value = studentStore.students.length
    groupCount.value = groupStore.groups.length
    taskCount.value = taskStore.tasks.length

    // 获取最近5个任务
    recentTasks.value = taskStore.tasks.slice(0, 5)
  })

  // 页面跳转
  const navigateTo = url => {
    uni.navigateTo({ url })
  }

  // 获取状态标签类型
  const getStatusType = status => {
    const types = {
      pending: 'warning',
      processing: 'primary',
      completed: 'success',
      failed: 'error'
    }
    return types[status] || 'warning'
  }
</script>

<style lang="scss" scoped>
  .stats-container {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10rpx;

    .stat-card {
      width: calc(50% - 20rpx);
      margin: 10rpx;
      background: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      text-align: center;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

      .stat-number {
        font-size: 36rpx;
        font-weight: bold;
        color: #2979ff;
        display: block;
        margin-bottom: 10rpx;
      }

      .stat-label {
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .quick-actions {
    margin-top: 40rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .action-grid {
      background: #fff;
      border-radius: 12rpx;
      padding: 20rpx 0;

      .grid-text {
        font-size: 28rpx;
        color: #333;
        margin-top: 10rpx;
      }
    }
  }

  .recent-tasks {
    margin-top: 40rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .task-card {
      background: #fff;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;

      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .task-name {
          font-size: 30rpx;
          font-weight: bold;
        }
      }

      .task-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
</style>
