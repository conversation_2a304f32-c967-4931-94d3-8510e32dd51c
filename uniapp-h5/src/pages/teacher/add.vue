<template>
  <view class="page-container">
    <u-navbar title="新增教师" :border="false" />

    <view class="form-container">
      <u-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="160rpx"
      >
        <!-- 教师姓名 -->
        <u-form-item label="姓名" prop="name" required>
          <u-input v-model="formData.name" placeholder="请输入教师姓名" :border="true" />
        </u-form-item>

        <!-- 可教授学科 -->
        <u-form-item label="可教授学科" prop="subjects" required>
          <view class="subjects-container">
            <u-checkbox-group v-model="formData.subjects">
              <u-checkbox
                v-for="subject in subjectOptions"
                :key="subject.value"
                :name="subject.value"
                :label="subject.label"
                shape="square"
                class="subject-checkbox"
              />
            </u-checkbox-group>

            <!-- 自定义学科输入 -->
            <view v-if="showCustomSubject" class="custom-subject">
              <u-input v-model="customSubject" placeholder="输入自定义学科" :border="true">
                <template #suffix>
                  <u-button type="primary" size="mini" @click="addCustomSubject">添加</u-button>
                </template>
              </u-input>
            </view>

            <u-button
              type="info"
              size="mini"
              icon="plus"
              text="添加其他学科"
              @click="showCustomSubject = true"
            />
          </view>
        </u-form-item>
      </u-form>

      <!-- 提交按钮 -->
      <view class="form-actions">
        <u-button type="primary" text="提交" :loading="submitting" @click="handleSubmit" />
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useTeacherStore } from '@/store'

  const teacherStore = useTeacherStore()

  // 表单数据
  const formData = reactive({
    name: '',
    subjects: []
  })

  // 表单校验规则
  const rules = {
    name: [{ required: true, message: '请输入教师姓名', trigger: ['blur', 'change'] }],
    subjects: [
      {
        required: true,
        message: '请选择至少一个学科',
        trigger: ['change'],
        type: 'array',
        min: 1
      }
    ]
  }

  // 学科选项
  const subjectOptions = ref([
    { label: '数学', value: '数学' },
    { label: '语文', value: '语文' },
    { label: '英语', value: '英语' },
    { label: '物理', value: '物理' },
    { label: '化学', value: '化学' },
    { label: '生物', value: '生物' }
  ])

  // 自定义学科
  const showCustomSubject = ref(false)
  const customSubject = ref('')

  // 表单实例
  const formRef = ref(null)

  // 提交状态
  const submitting = ref(false)

  // 添加自定义学科
  const addCustomSubject = () => {
    if (customSubject.value) {
      const newSubject = {
        label: customSubject.value,
        value: customSubject.value
      }
      subjectOptions.value.push(newSubject)
      formData.subjects.push(newSubject.value)
      customSubject.value = ''
      showCustomSubject.value = false
    }
  }

  // 提交表单
  const handleSubmit = () => {
    formRef.value?.validate(async valid => {
      if (valid) {
        submitting.value = true
        try {
          const success = await teacherStore.createTeacher({
            name: formData.name,
            subjects: formData.subjects
          })

          if (success) {
            uni.showToast({
              title: '添加成功',
              icon: 'success'
            })
            // 返回上一页
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          }
        } catch (error) {
          uni.showToast({
            title: '添加失败',
            icon: 'error'
          })
        } finally {
          submitting.value = false
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  .form-container {
    padding: 30rpx;
    background: #fff;
    border-radius: 12rpx;
    margin: 20rpx;
  }

  .subjects-container {
    .subject-checkbox {
      margin-right: 20rpx;
      margin-bottom: 20rpx;
    }

    .custom-subject {
      margin: 20rpx 0;
    }
  }

  .form-actions {
    margin-top: 40rpx;
    padding: 0 20rpx;
  }
</style>
