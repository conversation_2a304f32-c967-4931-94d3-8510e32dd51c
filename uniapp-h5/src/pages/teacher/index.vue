<template>
  <view class="page-container">
    <u-navbar title="教师管理" :border="false" />

    <!-- 操作栏 -->
    <view class="action-bar">
      <u-button
        type="primary"
        icon="plus"
        text="新增教师"
        @click="navigateTo('/pages/teacher/add')"
      />
    </view>

    <!-- 教师列表 -->
    <view v-if="!loading" class="teacher-list">
      <view v-for="teacher in teachers" :key="teacher.id" class="teacher-card">
        <view class="teacher-info">
          <view class="teacher-name">{{ teacher.name }}</view>
          <view class="teacher-subjects">
            <u-tag
              v-for="subject in teacher.subjects"
              :key="subject"
              :text="subject"
              type="info"
              size="mini"
              class="subject-tag"
            />
          </view>
        </view>
        <view class="teacher-actions">
          <u-button type="error" text="删除" size="mini" @click="handleDelete(teacher)" />
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <u-empty v-if="!loading && !teachers.length" mode="data" text="暂无教师数据" />

    <!-- 加载状态 -->
    <u-loading-page :loading="loading" />

    <!-- 删除确认弹窗 -->
    <u-modal
      :show="showDeleteModal"
      :title="'删除确认'"
      :content="'确定要删除该教师吗？'"
      :show-cancel-button="true"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </view>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useTeacherStore } from '@/store'

  const teacherStore = useTeacherStore()
  const { teachers, loading } = storeToRefs(teacherStore)

  // 删除相关
  const showDeleteModal = ref(false)
  const currentTeacher = ref(null)

  // 加载数据
  onMounted(() => {
    teacherStore.fetchTeachers()
  })

  // 页面跳转
  const navigateTo = url => {
    uni.navigateTo({ url })
  }

  // 处理删除
  const handleDelete = teacher => {
    currentTeacher.value = teacher
    showDeleteModal.value = true
  }

  const confirmDelete = async () => {
    if (currentTeacher.value) {
      // TODO: 实现删除API
      await teacherStore.deleteTeacher(currentTeacher.value.id)
      showDeleteModal.value = false
      currentTeacher.value = null
    }
  }

  const cancelDelete = () => {
    showDeleteModal.value = false
    currentTeacher.value = null
  }
</script>

<style lang="scss" scoped>
  .action-bar {
    padding: 20rpx;
    display: flex;
    justify-content: flex-end;
  }

  .teacher-list {
    padding: 0 20rpx;
  }

  .teacher-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .teacher-info {
      flex: 1;

      .teacher-name {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
      }

      .teacher-subjects {
        display: flex;
        flex-wrap: wrap;

        .subject-tag {
          margin-right: 10rpx;
          margin-bottom: 10rpx;
        }
      }
    }

    .teacher-actions {
      margin-left: 20rpx;
    }
  }
</style>
