<template>
  <view class="page-container">
    <u-navbar title="学生管理" :border="false" />

    <!-- 操作栏 -->
    <view class="action-bar">
      <u-button
        type="primary"
        icon="plus"
        text="新增学生"
        @click="navigateTo('/pages/student/add')"
      />
    </view>

    <!-- 学生列表 -->
    <view v-if="!loading" class="student-list">
      <!-- 搜索框 -->
      <view class="search-box">
        <u-search
          v-model="searchText"
          placeholder="搜索学生姓名"
          :show-action="false"
          @custom="onSearch"
          @change="onSearch"
        />
      </view>

      <!-- 列表内容 -->
      <view v-for="student in filteredStudents" :key="student.id" class="student-card">
        <view class="student-info">
          <text class="student-name">{{ student.name }}</text>
          <text class="student-id">ID: {{ student.id }}</text>
        </view>
        <view class="student-actions">
          <u-button type="error" text="删除" size="mini" @click="handleDelete(student)" />
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <u-empty v-if="!loading && !filteredStudents.length" mode="data" text="暂无学生数据" />

    <!-- 加载状态 -->
    <u-loading-page :loading="loading" />

    <!-- 删除确认弹窗 -->
    <u-modal
      :show="showDeleteModal"
      title="删除确认"
      :content="deleteModalContent"
      show-cancel-button
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </view>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useStudentStore } from '@/store'

  const studentStore = useStudentStore()
  const { students, loading } = storeToRefs(studentStore)

  // 搜索功能
  const searchText = ref('')
  const filteredStudents = computed(() => {
    if (!searchText.value) return students.value
    return students.value.filter(student =>
      student.name.toLowerCase().includes(searchText.value.toLowerCase())
    )
  })

  const onSearch = () => {
    // 搜索时可以添加其他逻辑
  }

  // 删除相关
  const showDeleteModal = ref(false)
  const currentStudent = ref(null)
  const deleteModalContent = computed(() =>
    currentStudent.value ? `确定要删除学生 ${currentStudent.value.name} 吗？` : ''
  )

  // 加载数据
  onMounted(() => {
    studentStore.fetchStudents()
  })

  // 页面跳转
  const navigateTo = url => {
    uni.navigateTo({ url })
  }

  // 处理删除
  const handleDelete = student => {
    currentStudent.value = student
    showDeleteModal.value = true
  }

  const confirmDelete = async () => {
    if (currentStudent.value) {
      // TODO: 实现删除API
      await studentStore.deleteStudent(currentStudent.value.id)
      showDeleteModal.value = false
      currentStudent.value = null
    }
  }

  const cancelDelete = () => {
    showDeleteModal.value = false
    currentStudent.value = null
  }
</script>

<style lang="scss" scoped>
  .action-bar {
    padding: 20rpx;
    display: flex;
    justify-content: flex-end;
  }

  .student-list {
    padding: 0 20rpx;

    .search-box {
      margin-bottom: 20rpx;
    }
  }

  .student-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .student-info {
      .student-name {
        font-size: 32rpx;
        font-weight: bold;
      }

      .student-id {
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
        display: block;
      }
    }

    .student-actions {
      margin-left: 20rpx;
    }
  }
</style>
