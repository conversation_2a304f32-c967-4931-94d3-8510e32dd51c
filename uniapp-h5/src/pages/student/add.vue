<template>
  <view class="page-container">
    <u-navbar title="新增学生" :border="false" />

    <view class="form-container">
      <u-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="160rpx"
      >
        <!-- 学生姓名 -->
        <u-form-item label="姓名" prop="name" required>
          <u-input v-model="formData.name" placeholder="请输入学生姓名" :border="true" />
        </u-form-item>
      </u-form>

      <!-- 提交按钮 -->
      <view class="form-actions">
        <u-button type="primary" text="提交" :loading="submitting" @click="handleSubmit" />
      </view>
    </view>
  </view>
</template>

<script>
  import { ref, reactive } from 'vue'
  import { useStudentStore } from '@/store'

  export default {
    setup() {
      const studentStore = useStudentStore()

      // 表单数据
      const formData = reactive({
        name: ''
      })

      // 表单校验规则
      const rules = {
        name: [{ required: true, message: '请输入学生姓名', trigger: ['blur', 'change'] }]
      }

      // 表单实例
      const formRef = ref(null)

      // 提交状态
      const submitting = ref(false)

      // 提交表单
      const handleSubmit = () => {
        formRef.value?.validate(async valid => {
          if (valid) {
            submitting.value = true
            try {
              const success = await studentStore.createStudent({
                name: formData.name
              })

              if (success) {
                uni.showToast({
                  title: '添加成功',
                  icon: 'success'
                })
                // 返回上一页
                setTimeout(() => {
                  uni.navigateBack()
                }, 1500)
              }
            } catch (error) {
              uni.showToast({
                title: '添加失败',
                icon: 'error'
              })
            } finally {
              submitting.value = false
            }
          }
        })
      }

      return {
        formData,
        rules,
        formRef,
        submitting,
        handleSubmit
      }
    }
  }
</script>

<style lang="scss" scoped>
  .form-container {
    padding: 30rpx;
    background: #fff;
    border-radius: 12rpx;
    margin: 20rpx;
  }

  .form-actions {
    margin-top: 40rpx;
    padding: 0 20rpx;
  }
</style>
