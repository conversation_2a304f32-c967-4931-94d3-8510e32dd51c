<template>
  <view class="page-container">
    <u-navbar title="新建任务" :border="false" />

    <view class="form-container">
      <u-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="160rpx"
      >
        <!-- 任务名称 -->
        <u-form-item label="任务名称" prop="name" required>
          <u-input v-model="formData.name" placeholder="请输入任务名称" :border="true" />
        </u-form-item>

        <!-- 任务说明 -->
        <u-form-item label="任务说明" prop="description">
          <u-textarea
            v-model="formData.description"
            placeholder="请输入任务说明（选填）"
            :height="200"
            count
            :maxlength="500"
          />
        </u-form-item>
      </u-form>

      <!-- 提示信息 -->
      <view class="tips">
        <u-alert type="info" title="任务创建后需要：" :show-icon="true">
          <text class="tip-item">1. 添加教师和学生的时间偏好</text>
          <text class="tip-item">2. 设置组-教师-学科匹配关系</text>
          <text class="tip-item">3. 发起排课</text>
        </u-alert>
      </view>

      <!-- 提交按钮 -->
      <view class="form-actions">
        <u-button type="primary" text="创建任务" :loading="submitting" @click="handleSubmit" />
      </view>
    </view>
  </view>
</template>

<script>
  import { ref, reactive } from 'vue'
  import { useTaskStore } from '@/store'

  export default {
    setup() {
      const taskStore = useTaskStore()

      // 表单数据
      const formData = reactive({
        name: '',
        description: ''
      })

      // 表单校验规则
      const rules = {
        name: [{ required: true, message: '请输入任务名称', trigger: ['blur', 'change'] }],
        description: [{ max: 500, message: '任务说明最多500字', trigger: ['blur', 'change'] }]
      }

      // 表单实例和提交状态
      const formRef = ref(null)
      const submitting = ref(false)

      // 提交表单
      const handleSubmit = () => {
        formRef.value?.validate(async valid => {
          if (valid) {
            submitting.value = true
            try {
              const task = await taskStore.createTask(formData)

              if (task) {
                uni.showToast({
                  title: '创建成功',
                  icon: 'success'
                })
                // 跳转到任务详情页
                setTimeout(() => {
                  uni.redirectTo({
                    url: `/pages/task/detail?id=${task.id}`
                  })
                }, 1500)
              }
            } catch (error) {
              uni.showToast({
                title: '创建失败',
                icon: 'error'
              })
            } finally {
              submitting.value = false
            }
          }
        })
      }

      return {
        formData,
        rules,
        formRef,
        submitting,
        handleSubmit
      }
    }
  }
</script>

<style lang="scss" scoped>
  .form-container {
    padding: 30rpx;
    background: #fff;
    border-radius: 12rpx;
    margin: 20rpx;
  }

  .tips {
    margin: 40rpx 0;

    .tip-item {
      display: block;
      margin-bottom: 8rpx;
      font-size: 28rpx;
      color: #666;
    }
  }

  .form-actions {
    margin-top: 40rpx;
    padding: 0 20rpx;
  }
</style>
