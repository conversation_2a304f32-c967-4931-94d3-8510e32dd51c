<template>
  <view class="page-container">
    <u-navbar :title="task?.name || '任务详情'" :border="false" />

    <!-- 任务状态 -->
    <view v-if="task" class="status-bar">
      <u-tag :text="getStatusText(task.status)" :type="getStatusType(task.status)" />
      <text class="create-time">创建于：{{ formatDate(task.createdAt) }}</text>
    </view>

    <!-- 标签页 -->
    <u-tabs :list="tabs" :current="currentTab" @change="handleTabChange">
      <!-- 时间偏好 -->
      <view v-if="currentTab === 0" class="tab-content">
        <view class="section">
          <view class="section-header">
            <text class="title">教师时间偏好</text>
            <u-button
              type="primary"
              icon="plus"
              text="添加"
              size="mini"
              @click="showTeacherPrefModal = true"
            />
          </view>

          <view v-if="teacherPreferences.length" class="preference-list">
            <view
              v-for="pref in teacherPreferences"
              :key="`t${pref.entityId}-${pref.day}-${pref.start}`"
              class="preference-card"
            >
              <view class="pref-header">
                <text class="teacher-name">{{ getTeacherName(pref.entityId) }}</text>
                <u-tag
                  :text="getPriorityText(pref.priority)"
                  :type="getPriorityType(pref.priority)"
                  size="mini"
                />
              </view>
              <view class="pref-time">{{ pref.day }} {{ pref.start }}-{{ pref.end }}</view>
              <view class="pref-actions">
                <u-button
                  type="error"
                  text="删除"
                  size="mini"
                  @click="handleDeletePreference(pref)"
                />
              </view>
            </view>
          </view>
          <u-empty v-else mode="data" text="暂无教师时间偏好" />
        </view>

        <view class="section">
          <view class="section-header">
            <text class="title">学生时间偏好</text>
            <u-button
              type="primary"
              icon="plus"
              text="添加"
              size="mini"
              @click="showStudentPrefModal = true"
            />
          </view>

          <view v-if="studentPreferences.length" class="preference-list">
            <view
              v-for="pref in studentPreferences"
              :key="`s${pref.entityId}-${pref.day}-${pref.start}`"
              class="preference-card"
            >
              <view class="pref-header">
                <text class="student-name">{{ getStudentName(pref.entityId) }}</text>
                <u-tag
                  :text="getPriorityText(pref.priority)"
                  :type="getPriorityType(pref.priority)"
                  size="mini"
                />
              </view>
              <view class="pref-time">{{ pref.day }} {{ pref.start }}-{{ pref.end }}</view>
              <view class="pref-actions">
                <u-button
                  type="error"
                  text="删除"
                  size="mini"
                  @click="handleDeletePreference(pref)"
                />
              </view>
            </view>
          </view>
          <u-empty v-else mode="data" text="暂无学生时间偏好" />
        </view>
      </view>

      <!-- 课程需求 -->
      <view v-if="currentTab === 1" class="tab-content">
        <view class="section">
          <view class="section-header">
            <text class="title">组-教师配对</text>
            <u-button
              type="primary"
              icon="plus"
              text="添加"
              size="mini"
              @click="showGroupTeacherModal = true"
            />
          </view>

          <view v-if="groupTeachers.length" class="demand-list">
            <view
              v-for="demand in groupTeachers"
              :key="`${demand.groupId}-${demand.teacherId}`"
              class="demand-card"
            >
              <view class="demand-info">
                <view class="group-name">
                  {{ getGroupName(demand.groupId) }}
                  <u-tag :text="demand.subject" type="primary" size="mini" />
                </view>
                <view class="teacher-name">
                  {{ getTeacherName(demand.teacherId) }}
                </view>
                <view class="duration">课程时长：{{ demand.duration }}分钟</view>
              </view>
              <view class="demand-actions">
                <u-button
                  type="error"
                  text="删除"
                  size="mini"
                  @click="handleDeleteDemand(demand)"
                />
              </view>
            </view>
          </view>
          <u-empty v-else mode="data" text="暂无课程需求" />
        </view>
      </view>

      <!-- 排课结果 -->
      <view v-if="currentTab === 2" class="tab-content">
        <!-- 操作按钮 -->
        <view class="result-actions">
          <u-button
            type="primary"
            text="开始排课"
            :loading="scheduling"
            :disabled="!canSchedule"
            @click="handleSchedule"
          />
        </view>

        <!-- 已排课程 -->
        <view class="section">
          <view class="section-header">
            <text class="title">已排课程 ({{ schedules.length }})</text>
          </view>

          <view v-if="schedules.length" class="schedule-list">
            <view
              v-for="schedule in sortedSchedules"
              :key="`${schedule.groupId}-${schedule.teacherId}-${schedule.start}`"
              class="schedule-card"
            >
              <view class="schedule-info">
                <view class="schedule-header">
                  <text class="subject">{{ schedule.subject }}</text>
                  <u-tag :text="`权重：${schedule.weight}`" type="info" size="mini" />
                </view>
                <view class="schedule-detail">
                  <view class="time">
                    {{ schedule.day }} {{ schedule.start }}-{{ schedule.end }}
                  </view>
                  <view class="teacher">教师：{{ schedule.teacher }}</view>
                  <view class="students">学生：{{ schedule.student_names.join(', ') }}</view>
                </view>
              </view>
            </view>
          </view>
          <u-empty v-else mode="data" text="暂无排课结果" />
        </view>

        <!-- 未排课程 -->
        <view v-if="unscheduled.length" class="section">
          <view class="section-header">
            <text class="title">未排课程 ({{ unscheduled.length }})</text>
          </view>

          <view class="unscheduled-list">
            <view v-for="(course, index) in unscheduled" :key="index" class="unscheduled-card">
              <view class="course-info">
                <view class="course-header">
                  <text class="subject">{{ course.subject }}</text>
                  <text class="group">{{ course.group }}</text>
                </view>
                <view class="course-teacher">教师：{{ course.teacher }}</view>
                <view class="course-students">学生：{{ course.student_names.join(', ') }}</view>
              </view>
              <view class="conflict-info">
                <text v-for="(reason, idx) in course.conflict_reasons" :key="idx" class="reason">
                  {{ reason }}
                </text>
                <text v-for="(suggestion, idx) in course.suggestions" :key="idx" class="suggestion">
                  建议：{{ suggestion }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-tabs>

    <!-- 教师时间偏好弹窗 -->
    <u-popup :show="showTeacherPrefModal" mode="center" @close="showTeacherPrefModal = false">
      <view class="pref-modal">
        <view class="modal-header">
          <text class="title">添加教师时间偏好</text>
        </view>
        <view class="modal-content">
          <u-form ref="teacherPrefFormRef" :model="teacherPrefForm">
            <u-form-item label="教师">
              <u-select
                v-model="teacherPrefForm.teacherId"
                :list="teacherOptions"
                placeholder="请选择教师"
              />
            </u-form-item>
            <u-form-item label="星期">
              <u-select v-model="teacherPrefForm.day" :list="dayOptions" placeholder="请选择星期" />
            </u-form-item>
            <u-form-item label="时间">
              <u-time-picker
                v-model="teacherPrefForm.time"
                :show="showTeacherTimePicker"
                mode="range"
                @confirm="onTeacherTimeConfirm"
                @cancel="showTeacherTimePicker = false"
              />
            </u-form-item>
            <u-form-item label="优先级">
              <u-radio-group v-model="teacherPrefForm.priority">
                <u-radio :name="1" label="首选时间" />
                <u-radio :name="3" label="普通时间" />
              </u-radio-group>
            </u-form-item>
          </u-form>
        </view>
        <view class="modal-footer">
          <u-button type="primary" text="确定" @click="handleAddTeacherPreference" />
          <u-button type="default" text="取消" @click="showTeacherPrefModal = false" />
        </view>
      </view>
    </u-popup>

    <!-- 学生时间偏好弹窗 -->
    <u-popup :show="showStudentPrefModal" mode="center" @close="showStudentPrefModal = false">
      <view class="pref-modal">
        <!-- 类似教师时间偏好弹窗的内容 -->
      </view>
    </u-popup>

    <!-- 组-教师配对弹窗 -->
    <u-popup :show="showGroupTeacherModal" mode="center" @close="showGroupTeacherModal = false">
      <view class="demand-modal">
        <view class="modal-header">
          <text class="title">添加课程需求</text>
        </view>
        <view class="modal-content">
          <u-form ref="demandFormRef" :model="demandForm">
            <u-form-item label="组">
              <u-select v-model="demandForm.groupId" :list="groupOptions" placeholder="请选择组" />
            </u-form-item>
            <u-form-item label="教师">
              <u-select
                v-model="demandForm.teacherId"
                :list="teacherOptions"
                placeholder="请选择教师"
              />
            </u-form-item>
            <u-form-item label="学科">
              <u-input v-model="demandForm.subject" placeholder="请输入学科" />
            </u-form-item>
            <u-form-item label="课程时长">
              <u-input
                v-model="demandForm.duration"
                type="number"
                placeholder="请输入时长（分钟）"
              />
            </u-form-item>
          </u-form>
        </view>
        <view class="modal-footer">
          <u-button type="primary" text="确定" @click="handleAddDemand" />
          <u-button type="default" text="取消" @click="showGroupTeacherModal = false" />
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useTaskStore, useTeacherStore, useStudentStore, useGroupStore } from '@/store'
  import { formatDate } from '@/utils/date'

  export default {
    setup() {
      const taskStore = useTaskStore()
      const teacherStore = useTeacherStore()
      const studentStore = useStudentStore()
      const groupStore = useGroupStore()

      // 当前任务数据
      const task = ref(null)
      const taskId = computed(() => {
        const pages = getCurrentPages()
        const page = pages[pages.length - 1]
        return page?.options?.id
      })

      // 标签页控制
      const tabs = [{ name: '时间偏好' }, { name: '课程需求' }, { name: '排课结果' }]
      const currentTab = ref(0)

      // 数据加载与状态
      const scheduling = ref(false)
      const canSchedule = computed(() => {
        return (
          task.value?.status === 'pending' &&
          teacherPreferences.value.length > 0 &&
          groupTeachers.value.length > 0
        )
      })

      // 各种弹窗控制
      const showTeacherPrefModal = ref(false)
      const showStudentPrefModal = ref(false)
      const showGroupTeacherModal = ref(false)
      const showTeacherTimePicker = ref(false)

      // 表单数据
      const teacherPrefForm = reactive({
        teacherId: '',
        day: '',
        time: [],
        priority: 1
      })

      const demandForm = reactive({
        groupId: '',
        teacherId: '',
        subject: '',
        duration: ''
      })

      // 选项数据
      const dayOptions = [
        { value: '周一', label: '周一' },
        { value: '周二', label: '周二' },
        { value: '周三', label: '周三' },
        { value: '周四', label: '周四' },
        { value: '周五', label: '周五' }
      ]

      const teacherOptions = computed(() =>
        teacherStore.teachers.map(t => ({
          value: t.id,
          label: t.name
        }))
      )

      const groupOptions = computed(() =>
        groupStore.groups.map(g => ({
          value: g.id,
          label: g.name
        }))
      )

      // 数据加载
      const loadData = async () => {
        if (!taskId.value) return

        await Promise.all([
          taskStore.fetchTaskDetail(taskId.value),
          teacherStore.fetchTeachers(),
          studentStore.fetchStudents(),
          groupStore.fetchGroups()
        ])

        task.value = taskStore.currentTask
      }

      // 事件处理
      const handleTabChange = index => {
        currentTab.value = index
      }

      const handleAddTeacherPreference = async () => {
        // TODO: 验证表单
        const [start, end] = teacherPrefForm.time
        const success = await taskStore.addTeacherPreference(
          taskId.value,
          teacherPrefForm.teacherId,
          {
            day: teacherPrefForm.day,
            start_time: start,
            end_time: end,
            priority: teacherPrefForm.priority
          }
        )

        if (success) {
          showTeacherPrefModal.value = false
          Object.assign(teacherPrefForm, {
            teacherId: '',
            day: '',
            time: [],
            priority: 1
          })
        }
      }

      const handleAddDemand = async () => {
        // TODO: 验证表单
        const success = await taskStore.addGroupTeacher(taskId.value, {
          group_id: demandForm.groupId,
          teacher_id: demandForm.teacherId,
          subject: demandForm.subject,
          duration: Number(demandForm.duration)
        })

        if (success) {
          showGroupTeacherModal.value = false
          Object.assign(demandForm, {
            groupId: '',
            teacherId: '',
            subject: '',
            duration: ''
          })
        }
      }

      const handleSchedule = async () => {
        if (!canSchedule.value) return

        scheduling.value = true
        try {
          const success = await taskStore.runScheduling(taskId.value)
          if (success) {
            currentTab.value = 2 // 切换到结果标签页
          }
        } finally {
          scheduling.value = false
        }
      }

      // 工具函数
      const getStatusText = status => {
        const texts = {
          pending: '待排课',
          processing: '排课中',
          completed: '已完成',
          failed: '失败'
        }
        return texts[status] || status
      }

      const getStatusType = status => {
        const types = {
          pending: 'warning',
          processing: 'primary',
          completed: 'success',
          failed: 'error'
        }
        return types[status] || 'warning'
      }

      const getPriorityText = priority => {
        return priority === 1 ? '首选' : '普通'
      }

      const getPriorityType = priority => {
        return priority === 1 ? 'primary' : 'info'
      }

      // 初始化
      onMounted(() => {
        loadData()
      })

      return {
        task,
        taskId,
        tabs,
        currentTab,
        scheduling,
        canSchedule,
        showTeacherPrefModal,
        showStudentPrefModal,
        showGroupTeacherModal,
        showTeacherTimePicker,
        teacherPrefForm,
        demandForm,
        dayOptions,
        teacherOptions,
        groupOptions,
        handleTabChange,
        handleAddTeacherPreference,
        handleAddDemand,
        handleSchedule,
        getStatusText,
        getStatusType,
        getPriorityText,
        getPriorityType,
        formatDate
      }
    }
  }
</script>

<style lang="scss" scoped>
  .status-bar {
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;

    .create-time {
      font-size: 24rpx;
      color: #999;
    }
  }

  .tab-content {
    min-height: 70vh;
  }

  .section {
    margin: 20rpx;
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .title {
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }

  .preference-card,
  .demand-card,
  .schedule-card,
  .unscheduled-card {
    border-bottom: 2rpx solid #eee;
    padding: 20rpx 0;

    &:last-child {
      border-bottom: none;
    }
  }

  .preference-card {
    .pref-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;
    }

    .pref-time {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 12rpx;
    }

    .pref-actions {
      display: flex;
      justify-content: flex-end;
    }
  }

  .demand-card {
    .demand-info {
      margin-bottom: 12rpx;

      .group-name {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }

      .teacher-name,
      .duration {
        font-size: 28rpx;
        color: #666;
      }
    }

    .demand-actions {
      display: flex;
      justify-content: flex-end;
    }
  }

  .schedule-card {
    .schedule-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      .subject {
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .schedule-detail {
      font-size: 28rpx;
      color: #666;

      .time {
        color: #2979ff;
        margin-bottom: 8rpx;
      }
    }
  }

  .unscheduled-card {
    .course-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      .subject {
        font-size: 32rpx;
        font-weight: bold;
      }

      .group {
        color: #666;
      }
    }

    .conflict-info {
      margin-top: 12rpx;
      padding-top: 12rpx;
      border-top: 2rpx solid #eee;

      .reason {
        display: block;
        font-size: 28rpx;
        color: #fa3534;
        margin-bottom: 8rpx;
      }

      .suggestion {
        display: block;
        font-size: 28rpx;
        color: #2979ff;
      }
    }
  }

  .modal-header {
    padding: 24rpx;
    text-align: center;
    border-bottom: 2rpx solid #eee;

    .title {
      font-size: 32rpx;
      font-weight: bold;
    }
  }

  .modal-content {
    padding: 24rpx;
    max-height: 60vh;
    overflow-y: auto;
  }

  .modal-footer {
    padding: 24rpx;
    display: flex;
    justify-content: center;
    gap: 20rpx;
    border-top: 2rpx solid #eee;
  }
</style>
