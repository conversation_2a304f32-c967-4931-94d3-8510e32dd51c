<template>
  <view class="page-container">
    <u-navbar title="排课任务" :border="false" />

    <!-- 操作栏 -->
    <view class="action-bar">
      <u-button type="primary" icon="plus" text="新建任务" @click="navigateTo('/pages/task/add')" />
    </view>

    <!-- 任务列表 -->
    <view v-if="!loading" class="task-list">
      <view
        v-for="task in tasks"
        :key="task.id"
        class="task-card"
        @click="navigateTo(`/pages/task/detail?id=${task.id}`)"
      >
        <view class="task-header">
          <text class="task-name">{{ task.name }}</text>
          <u-tag
            :text="getStatusText(task.status)"
            :type="getStatusType(task.status)"
            size="mini"
          />
        </view>

        <!-- 任务统计 -->
        <view class="task-stats">
          <view class="stat-item">
            <text class="label">已排课程</text>
            <text class="value">{{ task.scheduledCount || 0 }}</text>
          </view>
          <view class="stat-item">
            <text class="label">待排课程</text>
            <text class="value">{{ task.unscheduledCount || 0 }}</text>
          </view>
          <view class="stat-item">
            <text class="label">时间偏好</text>
            <text class="value">{{ task.preferenceCount || 0 }}</text>
          </view>
        </view>

        <!-- 任务操作 -->
        <view class="task-actions">
          <block v-if="task.status === 'pending'">
            <u-button
              type="primary"
              text="开始排课"
              size="mini"
              :disabled="!canRun(task)"
              @click.stop="handleRun(task)"
            />
          </block>
          <block v-else-if="task.status === 'completed'">
            <u-button
              type="success"
              icon="checkmark-circle"
              text="查看结果"
              size="mini"
              @click.stop="navigateTo(`/pages/task/detail?id=${task.id}&tab=result`)"
            />
          </block>
          <u-button type="error" text="删除" size="mini" @click.stop="handleDelete(task)" />
        </view>

        <view class="task-time">
          <u-icon name="clock" size="24" color="#999" />
          <text class="time-text">{{ formatDate(task.createdAt) }}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <u-empty v-if="!loading && !tasks.length" mode="data" text="暂无排课任务" />

    <!-- 加载状态 -->
    <u-loading-page :loading="loading" />

    <!-- 删除确认弹窗 -->
    <u-modal
      :show="showDeleteModal"
      title="删除确认"
      :content="deleteModalContent"
      show-cancel-button
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </view>
</template>

<script>
  import { ref, computed } from 'vue'
  import { useTaskStore } from '@/store'
  import { formatDate } from '@/utils/date'

  export default {
    setup() {
      const taskStore = useTaskStore()
      const { tasks, loading } = storeToRefs(taskStore)

      // 删除相关
      const showDeleteModal = ref(false)
      const currentTask = ref(null)
      const deleteModalContent = computed(() =>
        currentTask.value ? `确定要删除任务"${currentTask.value.name}"吗？` : ''
      )

      // 页面跳转
      const navigateTo = url => {
        uni.navigateTo({ url })
      }

      // 状态处理
      const getStatusText = status => {
        const texts = {
          pending: '待排课',
          processing: '排课中',
          completed: '已完成',
          failed: '失败'
        }
        return texts[status] || status
      }

      const getStatusType = status => {
        const types = {
          pending: 'warning',
          processing: 'primary',
          completed: 'success',
          failed: 'error'
        }
        return types[status] || 'warning'
      }

      // 检查是否可以开始排课
      const canRun = task => {
        return task.preferenceCount > 0
      }

      // 处理排课
      const handleRun = async task => {
        if (!canRun(task)) {
          uni.showToast({
            title: '请先添加时间偏好',
            icon: 'none'
          })
          return
        }

        uni.showModal({
          title: '开始排课',
          content: '确定要开始排课吗？',
          async success({ confirm }) {
            if (confirm) {
              const success = await taskStore.runScheduling(task.id)
              if (success) {
                uni.showToast({
                  title: '排课完成',
                  icon: 'success'
                })
                // 跳转到结果页面
                navigateTo(`/pages/task/detail?id=${task.id}&tab=result`)
              }
            }
          }
        })
      }

      // 处理删除
      const handleDelete = task => {
        currentTask.value = task
        showDeleteModal.value = true
      }

      const confirmDelete = async () => {
        if (currentTask.value) {
          const success = await taskStore.deleteTask(currentTask.value.id)
          if (success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
          showDeleteModal.value = false
          currentTask.value = null
        }
      }

      const cancelDelete = () => {
        showDeleteModal.value = false
        currentTask.value = null
      }

      // 初始加载数据
      onMounted(() => {
        taskStore.fetchTasks()
      })

      return {
        tasks,
        loading,
        showDeleteModal,
        deleteModalContent,
        navigateTo,
        getStatusText,
        getStatusType,
        canRun,
        handleRun,
        handleDelete,
        confirmDelete,
        cancelDelete,
        formatDate
      }
    }
  }
</script>

<style lang="scss" scoped>
  .action-bar {
    padding: 20rpx;
    display: flex;
    justify-content: flex-end;
  }

  .task-list {
    padding: 0 20rpx;
  }

  .task-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .task-name {
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .task-stats {
      display: flex;
      margin-bottom: 20rpx;

      .stat-item {
        flex: 1;
        text-align: center;

        .label {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 8rpx;
          display: block;
        }

        .value {
          font-size: 32rpx;
          color: #2979ff;
          font-weight: bold;
        }
      }
    }

    .task-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12rpx;
      margin-bottom: 16rpx;
    }

    .task-time {
      display: flex;
      align-items: center;

      .time-text {
        font-size: 24rpx;
        color: #999;
        margin-left: 8rpx;
      }
    }
  }
</style>
