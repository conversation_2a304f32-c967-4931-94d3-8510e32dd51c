<template>
  <view class="page-container">
    <u-navbar title="组管理" :border="false" />

    <!-- 操作栏 -->
    <view class="action-bar">
      <u-button type="primary" icon="plus" text="新增组" @click="navigateTo('/pages/group/add')" />
    </view>

    <!-- 组列表 -->
    <view v-if="!loading" class="group-list">
      <!-- 搜索框 -->
      <view class="search-box">
        <u-search
          v-model="searchText"
          placeholder="搜索组名"
          :show-action="false"
          @custom="onSearch"
          @change="onSearch"
        />
      </view>

      <!-- 列表内容 -->
      <view v-for="group in filteredGroups" :key="group.id" class="group-card">
        <view class="group-info">
          <view class="group-header">
            <text class="group-name">{{ group.name }}</text>
            <u-tag :text="group.subject" type="primary" size="mini" />
          </view>

          <view class="group-students">
            <text class="label">学生：</text>
            <view class="student-tags">
              <u-tag
                v-for="student in group.students"
                :key="student.id"
                :text="student.name"
                type="info"
                size="mini"
                class="student-tag"
              />
            </view>
          </view>
        </view>

        <view class="group-actions">
          <u-button
            type="primary"
            text="编辑"
            size="mini"
            class="action-btn"
            @click="handleEdit(group)"
          />
          <u-button
            type="error"
            text="删除"
            size="mini"
            class="action-btn"
            @click="handleDelete(group)"
          />
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <u-empty v-if="!loading && !filteredGroups.length" mode="data" text="暂无组数据" />

    <!-- 加载状态 -->
    <u-loading-page :loading="loading" />

    <!-- 删除确认弹窗 -->
    <u-modal
      :show="showDeleteModal"
      title="删除确认"
      :content="deleteModalContent"
      show-cancel-button
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </view>
</template>

<script>
  import { ref, computed } from 'vue'
  import { useGroupStore } from '@/store'

  export default {
    setup() {
      const groupStore = useGroupStore()
      const { groups, loading } = storeToRefs(groupStore)

      // 搜索功能
      const searchText = ref('')
      const filteredGroups = computed(() => {
        if (!searchText.value) return groups.value
        const searchLower = searchText.value.toLowerCase()
        return groups.value.filter(
          group =>
            group.name.toLowerCase().includes(searchLower) ||
            group.subject.toLowerCase().includes(searchLower)
        )
      })

      const onSearch = () => {
        // 搜索时可以添加其他逻辑
      }

      // 删除相关
      const showDeleteModal = ref(false)
      const currentGroup = ref(null)
      const deleteModalContent = computed(() =>
        currentGroup.value ? `确定要删除组 ${currentGroup.value.name} 吗？` : ''
      )

      // 页面跳转
      const navigateTo = url => {
        uni.navigateTo({ url })
      }

      // 处理编辑
      const handleEdit = group => {
        navigateTo(`/pages/group/add?id=${group.id}`)
      }

      // 处理删除
      const handleDelete = group => {
        currentGroup.value = group
        showDeleteModal.value = true
      }

      const confirmDelete = async () => {
        if (currentGroup.value) {
          const success = await groupStore.deleteGroup(currentGroup.value.id)
          if (success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
          showDeleteModal.value = false
          currentGroup.value = null
        }
      }

      const cancelDelete = () => {
        showDeleteModal.value = false
        currentGroup.value = null
      }

      // 初始加载数据
      onMounted(() => {
        groupStore.fetchGroups()
      })

      return {
        groups,
        loading,
        searchText,
        filteredGroups,
        showDeleteModal,
        deleteModalContent,
        onSearch,
        navigateTo,
        handleEdit,
        handleDelete,
        confirmDelete,
        cancelDelete
      }
    }
  }
</script>

<style lang="scss" scoped>
  .action-bar {
    padding: 20rpx;
    display: flex;
    justify-content: flex-end;
  }

  .group-list {
    padding: 0 20rpx;

    .search-box {
      margin-bottom: 20rpx;
    }
  }

  .group-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;

    .group-info {
      margin-bottom: 20rpx;

      .group-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .group-name {
          font-size: 32rpx;
          font-weight: bold;
          margin-right: 16rpx;
        }
      }

      .group-students {
        display: flex;
        align-items: flex-start;

        .label {
          font-size: 28rpx;
          color: #666;
          margin-right: 12rpx;
          flex-shrink: 0;
        }

        .student-tags {
          flex: 1;
          display: flex;
          flex-wrap: wrap;

          .student-tag {
            margin-right: 12rpx;
            margin-bottom: 12rpx;
          }
        }
      }
    }

    .group-actions {
      display: flex;
      justify-content: flex-end;

      .action-btn {
        margin-left: 12rpx;
      }
    }
  }
</style>
