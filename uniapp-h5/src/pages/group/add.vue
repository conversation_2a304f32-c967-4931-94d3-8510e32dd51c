<template>
  <view class="page-container">
    <u-navbar :title="isEdit ? '编辑组' : '新增组'" :border="false" />

    <view class="form-container">
      <u-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="160rpx"
      >
        <!-- 组名 -->
        <u-form-item label="组名" prop="name" required>
          <u-input v-model="formData.name" placeholder="请输入组名" :border="true" />
        </u-form-item>

        <!-- 学科选择 -->
        <u-form-item label="学科" prop="subject" required>
          <u-picker
            :show="showSubjectPicker"
            :columns="[subjectOptions]"
            @confirm="onSubjectConfirm"
            @cancel="showSubjectPicker = false"
          />
          <view class="picker-trigger" @click="showSubjectPicker = true">
            <text :class="{ placeholder: !formData.subject }">
              {{ formData.subject || '请选择学科' }}
            </text>
            <u-icon name="arrow-right" color="#666" size="28" />
          </view>
        </u-form-item>

        <!-- 学生选择 -->
        <u-form-item label="学生" prop="studentIds" required>
          <view class="student-selector">
            <!-- 已选学生展示 -->
            <view v-if="selectedStudents.length" class="selected-students">
              <view v-for="student in selectedStudents" :key="student.id" class="student-tag">
                <u-tag
                  :text="student.name"
                  type="primary"
                  closeable
                  size="mini"
                  @close="removeStudent(student)"
                />
              </view>
            </view>

            <!-- 选择按钮 -->
            <u-button
              type="info"
              size="mini"
              icon="plus"
              text="选择学生"
              @click="showStudentPicker = true"
            />
          </view>
        </u-form-item>
      </u-form>

      <!-- 提交按钮 -->
      <view class="form-actions">
        <u-button
          type="primary"
          :text="isEdit ? '保存' : '提交'"
          :loading="submitting"
          @click="handleSubmit"
        />
      </view>
    </view>

    <!-- 学生选择弹窗 -->
    <u-popup :show="showStudentPicker" mode="bottom" @close="showStudentPicker = false">
      <view class="student-picker">
        <view class="picker-header">
          <text class="title">选择学生</text>
          <u-button type="primary" text="完成" size="mini" @click="confirmStudentSelection" />
        </view>
        <view class="student-list">
          <u-checkbox-group v-model="formData.studentIds">
            <view v-for="student in availableStudents" :key="student.id" class="student-item">
              <u-checkbox :name="student.id" :label="student.name" />
            </view>
          </u-checkbox-group>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useGroupStore, useStudentStore } from '@/store'

  export default {
    setup() {
      const groupStore = useGroupStore()
      const studentStore = useStudentStore()

      // 页面参数，判断是否为编辑模式
      const isEdit = computed(() => {
        const pages = getCurrentPages()
        const page = pages[pages.length - 1]
        return !!page?.options?.id
      })

      // 表单数据
      const formData = reactive({
        name: '',
        subject: '',
        studentIds: []
      })

      // 表单校验规则
      const rules = {
        name: [{ required: true, message: '请输入组名', trigger: ['blur', 'change'] }],
        subject: [{ required: true, message: '请选择学科', trigger: ['blur', 'change'] }],
        studentIds: [
          {
            required: true,
            type: 'array',
            min: 1,
            message: '请至少选择一个学生',
            trigger: ['change']
          }
        ]
      }

      // 学科选项
      const subjectOptions = ['数学', '语文', '英语', '物理', '化学', '生物']
      const showSubjectPicker = ref(false)

      // 学生选择
      const showStudentPicker = ref(false)
      const availableStudents = computed(() => studentStore.students)
      const selectedStudents = computed(() =>
        availableStudents.value.filter(s => formData.studentIds.includes(s.id))
      )

      // 表单实例和提交状态
      const formRef = ref(null)
      const submitting = ref(false)

      // 学科选择确认
      const onSubjectConfirm = ([subject]) => {
        formData.subject = subject
        showSubjectPicker.value = false
      }

      // 移除已选学生
      const removeStudent = student => {
        const index = formData.studentIds.indexOf(student.id)
        if (index > -1) {
          formData.studentIds.splice(index, 1)
        }
      }

      // 确认学生选择
      const confirmStudentSelection = () => {
        showStudentPicker.value = false
      }

      // 提交表单
      const handleSubmit = () => {
        formRef.value?.validate(async valid => {
          if (valid) {
            submitting.value = true
            try {
              let success
              if (isEdit.value) {
                const id = getCurrentPages()[getCurrentPages().length - 1].options.id
                success = await groupStore.updateGroup(id, formData)
              } else {
                success = await groupStore.createGroup(formData)
              }

              if (success) {
                uni.showToast({
                  title: isEdit.value ? '保存成功' : '创建成功',
                  icon: 'success'
                })
                // 返回上一页
                setTimeout(() => {
                  uni.navigateBack()
                }, 1500)
              }
            } catch (error) {
              uni.showToast({
                title: isEdit.value ? '保存失败' : '创建失败',
                icon: 'error'
              })
            } finally {
              submitting.value = false
            }
          }
        })
      }

      // 如果是编辑模式，加载现有数据
      onMounted(async () => {
        if (isEdit.value) {
          const pages = getCurrentPages()
          const page = pages[pages.length - 1]
          const id = page.options.id

          // 加载组数据
          const group = await groupStore.getGroupById(id)
          if (group) {
            formData.name = group.name
            formData.subject = group.subject
            formData.studentIds = group.students.map(s => s.id)
          }
        }

        // 加载学生列表
        await studentStore.fetchStudents()
      })

      return {
        isEdit,
        formData,
        rules,
        formRef,
        submitting,
        subjectOptions,
        showSubjectPicker,
        showStudentPicker,
        availableStudents,
        selectedStudents,
        onSubjectConfirm,
        removeStudent,
        confirmStudentSelection,
        handleSubmit
      }
    }
  }
</script>

<style lang="scss" scoped>
  .form-container {
    padding: 30rpx;
    background: #fff;
    border-radius: 12rpx;
    margin: 20rpx;
  }

  .picker-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 0;

    .placeholder {
      color: #999;
    }
  }

  .student-selector {
    .selected-students {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20rpx;

      .student-tag {
        margin-right: 16rpx;
        margin-bottom: 16rpx;
      }
    }
  }

  .student-picker {
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;

    .picker-header {
      padding: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2rpx solid #eee;

      .title {
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .student-list {
      padding: 24rpx;
      max-height: 60vh;
      overflow-y: auto;

      .student-item {
        padding: 16rpx 0;
      }
    }
  }

  .form-actions {
    margin-top: 40rpx;
  }
</style>
