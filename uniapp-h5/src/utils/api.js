const BASE_URL = 'http://localhost:8080/api'

// 请求拦截器
const requestInterceptor = config => {
  // 可以在这里添加token等
  return config
}

// 响应拦截器
const responseInterceptor = response => {
  const { data } = response
  if (data.code !== 200) {
    uni.showToast({
      title: data.message || '请求失败',
      icon: 'none'
    })
    return Promise.reject(data)
  }
  return data.data
}

// 封装请求方法
const request = options => {
  options.url = BASE_URL + options.url
  options = requestInterceptor(options)

  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      success: res => {
        resolve(responseInterceptor(res))
      },
      fail: err => {
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// 教师相关接口
export const teacherAPI = {
  // 创建教师
  create: data =>
    request({
      url: '/teachers',
      method: 'POST',
      data
    }),

  // 获取教师列表
  list: () =>
    request({
      url: '/teachers',
      method: 'GET'
    })
}

// 学生相关接口
export const studentAPI = {
  // 创建学生
  create: data =>
    request({
      url: '/students',
      method: 'POST',
      data
    }),

  // 获取学生列表
  list: () =>
    request({
      url: '/students',
      method: 'GET'
    })
}

// 组相关接口
export const groupAPI = {
  // 创建组
  create: data =>
    request({
      url: '/groups',
      method: 'POST',
      data
    }),

  // 获取组列表
  list: () =>
    request({
      url: '/groups',
      method: 'GET'
    })
}

// 排课任务相关接口
export const taskAPI = {
  // 创建任务
  create: data =>
    request({
      url: '/tasks',
      method: 'POST',
      data
    }),

  // 获取任务列表
  list: () =>
    request({
      url: '/tasks',
      method: 'GET'
    }),

  // 获取任务详情
  detail: taskId =>
    request({
      url: `/tasks/${taskId}/status`,
      method: 'GET'
    }),

  // 添加教师时间偏好
  addTeacherPreference: (taskId, teacherId, data) =>
    request({
      url: `/tasks/${taskId}/preferences/teachers/${teacherId}`,
      method: 'POST',
      data
    }),

  // 添加学生时间偏好
  addStudentPreference: (taskId, studentId, data) =>
    request({
      url: `/tasks/${taskId}/preferences/students/${studentId}`,
      method: 'POST',
      data
    }),

  // 添加课程需求
  addGroupTeacher: (taskId, data) =>
    request({
      url: `/tasks/${taskId}/group-teacher`,
      method: 'POST',
      data
    }),

  // 发起排课
  run: taskId =>
    request({
      url: `/tasks/${taskId}/run`,
      method: 'POST'
    }),

  // 获取排课结果
  getSchedules: taskId =>
    request({
      url: `/tasks/${taskId}/schedules`,
      method: 'GET'
    }),

  // 获取未排课程
  getUnscheduled: taskId =>
    request({
      url: `/tasks/${taskId}/unscheduled`,
      method: 'GET'
    })
}
