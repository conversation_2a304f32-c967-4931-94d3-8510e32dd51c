/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $u: any
  }
}

// Uni-app API声明
declare const uni: any
declare const wx: any

// 环境变量声明
declare const process: {
  env: {
    NODE_ENV: string
    VUE_APP_BASE_API: string
    [key: string]: string
  }
}