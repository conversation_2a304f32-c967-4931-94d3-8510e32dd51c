import { defineStore } from 'pinia'
import { teacherAPI, studentAPI, groupAPI, taskAPI } from '@/utils/api'

// 教师状态管理
export const useTeacherStore = defineStore('teacher', {
  state: () => ({
    teachers: [],
    loading: false
  }),

  actions: {
    async fetchTeachers() {
      this.loading = true
      try {
        this.teachers = await teacherAPI.list()
      } catch (error) {
        console.error('获取教师列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    async createTeacher(teacher) {
      try {
        await teacherAPI.create(teacher)
        await this.fetchTeachers()
        return true
      } catch (error) {
        console.error('创建教师失败:', error)
        return false
      }
    }
  }
})

// 学生状态管理
export const useStudentStore = defineStore('student', {
  state: () => ({
    students: [],
    loading: false
  }),

  actions: {
    async fetchStudents() {
      this.loading = true
      try {
        this.students = await studentAPI.list()
      } catch (error) {
        console.error('获取学生列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    async createStudent(student) {
      try {
        await studentAPI.create(student)
        await this.fetchStudents()
        return true
      } catch (error) {
        console.error('创建学生失败:', error)
        return false
      }
    }
  }
})

// 组状态管理
export const useGroupStore = defineStore('group', {
  state: () => ({
    groups: [],
    loading: false
  }),

  actions: {
    async fetchGroups() {
      this.loading = true
      try {
        this.groups = await groupAPI.list()
      } catch (error) {
        console.error('获取组列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    async createGroup(group) {
      try {
        await groupAPI.create(group)
        await this.fetchGroups()
        return true
      } catch (error) {
        console.error('创建组失败:', error)
        return false
      }
    }
  }
})

// 任务状态管理
export const useTaskStore = defineStore('task', {
  state: () => ({
    tasks: [],
    currentTask: null,
    schedules: [],
    unscheduled: [],
    loading: false
  }),

  actions: {
    async fetchTasks() {
      this.loading = true
      try {
        this.tasks = await taskAPI.list()
      } catch (error) {
        console.error('获取任务列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    async createTask(task) {
      try {
        await taskAPI.create(task)
        await this.fetchTasks()
        return true
      } catch (error) {
        console.error('创建任务失败:', error)
        return false
      }
    },

    async fetchTaskDetail(taskId) {
      this.loading = true
      try {
        this.currentTask = await taskAPI.detail(taskId)
      } catch (error) {
        console.error('获取任务详情失败:', error)
      } finally {
        this.loading = false
      }
    },

    async addTeacherPreference(taskId, teacherId, data) {
      try {
        await taskAPI.addTeacherPreference(taskId, teacherId, data)
        await this.fetchTaskDetail(taskId)
        return true
      } catch (error) {
        console.error('添加教师时间偏好失败:', error)
        return false
      }
    },

    async addStudentPreference(taskId, studentId, data) {
      try {
        await taskAPI.addStudentPreference(taskId, studentId, data)
        await this.fetchTaskDetail(taskId)
        return true
      } catch (error) {
        console.error('添加学生时间偏好失败:', error)
        return false
      }
    },

    async addGroupTeacher(taskId, data) {
      try {
        await taskAPI.addGroupTeacher(taskId, data)
        await this.fetchTaskDetail(taskId)
        return true
      } catch (error) {
        console.error('添加课程需求失败:', error)
        return false
      }
    },

    async runScheduling(taskId) {
      this.loading = true
      try {
        await taskAPI.run(taskId)
        // 获取排课结果
        const [schedules, unscheduled] = await Promise.all([
          taskAPI.getSchedules(taskId),
          taskAPI.getUnscheduled(taskId)
        ])
        this.schedules = schedules
        this.unscheduled = unscheduled
        return true
      } catch (error) {
        console.error('排课失败:', error)
        return false
      } finally {
        this.loading = false
      }
    }
  }
})
