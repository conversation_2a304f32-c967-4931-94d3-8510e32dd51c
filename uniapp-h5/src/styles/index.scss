@import 'uview-plus/theme.scss';
@import './variables.scss';

// 重置样式
page {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-size: $font-size-base;
  color: $text-color-primary;
  line-height: 1.5;
  background-color: $bg-color-page;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

// 工具类
.flex {
  display: flex;
  &-center {
    @include flex-center;
  }
  &-between {
    @include flex-between;
  }
  &-column {
    flex-direction: column;
  }
  &-wrap {
    flex-wrap: wrap;
  }
}

.text {
  &-primary {
    color: $color-primary;
  }
  &-success {
    color: $color-success;
  }
  &-warning {
    color: $color-warning;
  }
  &-error {
    color: $color-error;
  }
  &-ellipsis {
    @include text-ellipsis;
  }
}

.bg {
  &-primary {
    background-color: $color-primary;
  }
  &-white {
    background-color: $bg-color-white;
  }
}

.spacing {
  &-base {
    margin: $spacing-base;
    padding: $spacing-base;
  }
  &-large {
    margin: $spacing-large;
    padding: $spacing-large;
  }
  &-small {
    margin: $spacing-small;
    padding: $spacing-small;
  }
}

.card {
  background-color: $bg-color-white;
  border-radius: $border-radius-base;
  padding: $spacing-base;
  margin-bottom: $spacing-base;
  box-shadow: $box-shadow-base;
}

.form {
  &-item {
    margin-bottom: $spacing-base;
  }
  &-label {
    font-size: $font-size-base;
    color: $text-color-regular;
    margin-bottom: $spacing-mini;
  }
}

.btn {
  &-primary {
    background-color: $color-primary !important;
    color: $bg-color-white !important;
  }
  &-success {
    background-color: $color-success !important;
    color: $bg-color-white !important;
  }
  &-warning {
    background-color: $color-warning !important;
    color: $bg-color-white !important;
  }
  &-error {
    background-color: $color-error !important;
    color: $bg-color-white !important;
  }
}
