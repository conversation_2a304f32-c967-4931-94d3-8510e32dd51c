// 颜色变量
$color-primary: #2979ff;
$color-success: #19be6b;
$color-warning: #ff9900;
$color-error: #fa3534;

// 文字颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// 边框颜色
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// 背景颜色
$bg-color-page: #f5f6fa;
$bg-color-white: #ffffff;

// 尺寸
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 圆角
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-circle: 100%;

// 间距
$spacing-base: 24rpx;
$spacing-large: 32rpx;
$spacing-small: 16rpx;
$spacing-mini: 8rpx;

// 阴影
$box-shadow-base: 0 2px 12px rgba(0, 0, 0, 0.04);
$box-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.02);
$box-shadow-dark: 0 4px 16px rgba(0, 0, 0, 0.08);

// z-index
$zindex-normal: 1;
$zindex-dropdown: 1000;
$zindex-sticky: 1020;
$zindex-fixed: 1030;
$zindex-modal-backdrop: 1040;
$zindex-modal: 1050;
$zindex-popover: 1060;
$zindex-tooltip: 1070;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@mixin multi-ellipsis($line: 2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  overflow: hidden;
}

@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}