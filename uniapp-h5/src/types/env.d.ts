/// <reference types="@dcloudio/types" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 环境变量类型声明
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_BASE_API: string
  readonly VITE_APP_ENV: string
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// uni-app API全局声明
declare const uni: UniApp.Uni
declare const wx: any

// 全局组件类型声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $u: any
  }
}

// uview-plus组件类型声明
declare module 'uview-plus' {
  import { Plugin } from 'vue'
  const uviewPlus: Plugin
  export default uviewPlus
}

// 通用类型声明
type Recordable<T = any> = Record<string, T>
type Nullable<T> = T | null
type NonNullable<T> = T extends null | undefined ? never : T
type Arrayable<T> = T | T[]
type TimeoutHandle = ReturnType<typeof setTimeout>
type IntervalHandle = ReturnType<typeof setInterval>

// 通用响应类型
interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
}

// 分页请求参数
interface PageParams {
  page: number
  pageSize: number
  [key: string]: any
}

// 分页响应数据
interface PageResult<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}
