import { createSSRApp } from 'vue'
import VueCompositionAPI from '@vue/composition-api'
import App from './App.vue'
import { createPinia } from 'pinia'
import uviewPlus from 'uview-plus'
import '@dcloudio/uni-h5'

// 创建应用实例
export function createApp() {
  const app = createSSRApp(App)
  app.use(VueCompositionAPI)

  // 使用Pinia状态管理
  const pinia = createPinia()
  app.use(pinia)

  // 使用uview-plus组件库
  app.use(uviewPlus)

  return {
    app,
    pinia
  }
}

// 全局错误处理
const errorHandler = (err: any, vm: any, info: string) => {
  console.error('Vue Error:', err, vm, info)
  uni.showToast({
    title: '系统错误',
    icon: 'none'
  })
}

const app = createApp()
app.app.config.errorHandler = errorHandler
app.app.config.warnHandler = (msg: string) => {
  console.warn('Vue Warning:', msg)
}
