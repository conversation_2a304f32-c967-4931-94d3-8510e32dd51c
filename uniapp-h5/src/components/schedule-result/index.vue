<template>
  <view class="schedule-result">
    <!-- 视图切换 -->
    <u-tabs :list="viewTabs" :current="currentView" @change="handleViewChange" />

    <!-- 按天视图 -->
    <view v-if="currentView === 0" class="day-view">
      <view v-for="day in sortedDays" :key="day" class="day-group">
        <view class="day-header">{{ day }}</view>
        <view
          v-for="schedule in schedulesByDay[day]"
          :key="`${schedule.teacher}-${schedule.start}`"
          class="time-slot"
        >
          <view class="time-header">{{ schedule.start }} - {{ schedule.end }}</view>
          <view class="schedule-card">
            <view class="schedule-subject">{{ schedule.subject }}</view>
            <view class="schedule-info">
              <text>教师：{{ schedule.teacher }}</text>
              <text>组：{{ schedule.group }}</text>
            </view>
            <view class="schedule-students">
              {{ schedule.student_names.join(', ') }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 按教师视图 -->
    <view v-if="currentView === 1" class="teacher-view">
      <view v-for="(schedules, teacher) in schedulesByTeacher" :key="teacher" class="teacher-group">
        <view class="teacher-header">{{ teacher }}</view>
        <view
          v-for="schedule in sortSchedules(schedules)"
          :key="`${schedule.day}-${schedule.start}`"
          class="time-slot"
        >
          <view class="time-header">
            {{ schedule.day }} {{ schedule.start }} - {{ schedule.end }}
          </view>
          <view class="schedule-card">
            <view class="schedule-subject">{{ schedule.subject }}</view>
            <view class="schedule-info">
              <text>组：{{ schedule.group }}</text>
            </view>
            <view class="schedule-students">
              {{ schedule.student_names.join(', ') }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 按组视图 -->
    <view v-if="currentView === 2" class="group-view">
      <view v-for="(schedules, group) in schedulesByGroup" :key="group" class="group-group">
        <view class="group-header">{{ group }}</view>
        <view
          v-for="schedule in sortSchedules(schedules)"
          :key="`${schedule.day}-${schedule.start}`"
          class="time-slot"
        >
          <view class="time-header">
            {{ schedule.day }} {{ schedule.start }} - {{ schedule.end }}
          </view>
          <view class="schedule-card">
            <view class="schedule-subject">{{ schedule.subject }}</view>
            <view class="schedule-info">
              <text>教师：{{ schedule.teacher }}</text>
            </view>
            <view class="schedule-students">
              {{ schedule.student_names.join(', ') }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 未排课程 -->
    <view v-if="unscheduled.length" class="unscheduled-section">
      <view class="section-header">
        <text class="title">未排课程 ({{ unscheduled.length }})</text>
      </view>
      <view v-for="(course, index) in unscheduled" :key="index" class="unscheduled-card">
        <view class="course-info">
          <view class="course-header">
            <text class="subject">{{ course.subject }}</text>
            <text class="group">{{ course.group }}</text>
          </view>
          <view class="course-teacher">教师：{{ course.teacher }}</view>
          <view class="course-students">学生：{{ course.student_names.join(', ') }}</view>
        </view>
        <view class="conflict-info">
          <text v-for="(reason, idx) in course.conflict_reasons" :key="idx" class="reason">
            {{ reason }}
          </text>
          <text v-for="(suggestion, idx) in course.suggestions" :key="idx" class="suggestion">
            建议：{{ suggestion }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { ref, computed } from 'vue'

  export default {
    name: 'ScheduleResult',

    props: {
      schedules: {
        type: Array,
        default: () => []
      },
      unscheduled: {
        type: Array,
        default: () => []
      }
    },

    setup(props) {
      // 视图控制
      const viewTabs = [{ name: '按天查看' }, { name: '按教师查看' }, { name: '按组查看' }]
      const currentView = ref(0)

      // 数据处理
      const sortedDays = ['周一', '周二', '周三', '周四', '周五']

      // 按天分组
      const schedulesByDay = computed(() => {
        const result = {}
        sortedDays.forEach(day => {
          result[day] = props.schedules.filter(s => s.day === day)
        })
        return result
      })

      // 按教师分组
      const schedulesByTeacher = computed(() => {
        const result = {}
        props.schedules.forEach(schedule => {
          if (!result[schedule.teacher]) {
            result[schedule.teacher] = []
          }
          result[schedule.teacher].push(schedule)
        })
        return result
      })

      // 按组分组
      const schedulesByGroup = computed(() => {
        const result = {}
        props.schedules.forEach(schedule => {
          if (!result[schedule.group]) {
            result[schedule.group] = []
          }
          result[schedule.group].push(schedule)
        })
        return result
      })

      // 排序规则
      const sortSchedules = schedules => {
        return [...schedules].sort((a, b) => {
          const dayDiff = sortedDays.indexOf(a.day) - sortedDays.indexOf(b.day)
          if (dayDiff !== 0) return dayDiff
          return a.start.localeCompare(b.start)
        })
      }

      // 视图切换
      const handleViewChange = index => {
        currentView.value = index
      }

      return {
        viewTabs,
        currentView,
        sortedDays,
        schedulesByDay,
        schedulesByTeacher,
        schedulesByGroup,
        sortSchedules,
        handleViewChange
      }
    }
  }
</script>

<style lang="scss" scoped>
  .schedule-result {
    .day-view,
    .teacher-view,
    .group-view {
      padding: 20rpx;
    }

    .day-header,
    .teacher-header,
    .group-header {
      font-size: 32rpx;
      font-weight: bold;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
      margin-bottom: 16rpx;
    }

    .time-slot {
      margin-bottom: 20rpx;

      .time-header {
        font-size: 28rpx;
        color: #2979ff;
        margin-bottom: 8rpx;
      }

      .schedule-card {
        background: #fff;
        padding: 20rpx;
        border-radius: 8rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

        .schedule-subject {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 12rpx;
        }

        .schedule-info {
          font-size: 28rpx;
          color: #666;
          margin-bottom: 8rpx;

          text {
            margin-right: 20rpx;
          }
        }

        .schedule-students {
          font-size: 26rpx;
          color: #999;
        }
      }
    }

    .unscheduled-section {
      margin-top: 40rpx;
      padding: 20rpx;

      .section-header {
        margin-bottom: 20rpx;

        .title {
          font-size: 32rpx;
          font-weight: bold;
          color: #fa3534;
        }
      }

      .unscheduled-card {
        background: #fff;
        padding: 20rpx;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

        .course-info {
          margin-bottom: 16rpx;

          .course-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12rpx;

            .subject {
              font-size: 32rpx;
              font-weight: bold;
            }

            .group {
              font-size: 28rpx;
              color: #666;
            }
          }

          .course-teacher,
          .course-students {
            font-size: 28rpx;
            color: #666;
          }
        }

        .conflict-info {
          border-top: 2rpx solid #eee;
          padding-top: 16rpx;

          .reason {
            display: block;
            font-size: 28rpx;
            color: #fa3534;
            margin-bottom: 8rpx;
          }

          .suggestion {
            display: block;
            font-size: 28rpx;
            color: #2979ff;
            margin-bottom: 8rpx;
          }
        }
      }
    }
  }
</style>
