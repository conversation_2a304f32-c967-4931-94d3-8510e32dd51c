# 排课系统前端

基于 Vue3 + Vite + uView 开发的排课系统 H5 前端。

## 功能特点

- 教师管理：增删查教师，设置可授学科
- 学生管理：增删查学生
- 组管理：增删查组，分配学生
- 排课任务：
  - 创建排课任务
  - 添加时间偏好（教师/学生）
  - 设置课程需求（组-教师配对）
  - 执行排课
  - 查看排课结果（已排/未排/冲突）

## 环境要求

- Node.js >= 14.0.0
- pnpm >= 7.0.0

## 快速开始

1. 安装依赖：
```bash
# 使用start.sh脚本（推荐）
chmod +x start.sh
./start.sh

# 或手动安装
pnpm install
```

2. 开发模式：
```bash
pnpm serve
```

3. 构建生产版本：
```bash
pnpm build
```

## 项目结构

```
uniapp-h5/
├── src/
│   ├── components/        # 通用组件
│   │   └── schedule-result/  # 排课结果展示组件
│   ├── pages/            # 页面文件
│   │   ├── index/        # 首页
│   │   ├── teacher/      # 教师管理
│   │   ├── student/      # 学生管理
│   │   ├── group/        # 组管理
│   │   └── task/         # 任务管理
│   ├── store/            # Pinia状态管理
│   ├── utils/            # 工具函数
│   ├── App.vue           # 应用入口
│   ├── main.js           # 主入口
│   └── pages.json        # 页面配置
├── .babelrc              # Babel配置
├── .gitignore           # Git忽略配置
├── index.html           # HTML模板
├── package.json         # 项目配置
├── postcss.config.js    # PostCSS配置
├── README.md           # 项目说明
├── start.sh            # 启动脚本
└── vue.config.js       # Vue配置
```

## 开发说明

1. 目录规范：
   - 组件统一放在 components 目录
   - 页面统一放在 pages 目录
   - 工具方法统一放在 utils 目录
   - 状态管理统一放在 store 目录

2. 代码规范：
   - 使用 ESLint + Prettier 进行代码格式化
   - 使用 TypeScript 类型检查（可选）
   - 组件使用 Composition API 风格

3. 开发流程：
   - 本地开发使用 `pnpm serve`
   - 提交前运行 `pnpm lint` 检查代码规范
   - 发布前运行 `pnpm build` 构建生产版本

## 接口对接

后端API配置在 `src/utils/api.js` 中：

```javascript
const BASE_URL = 'http://localhost:8080/api'
```

开发时需要同时运行：
1. 后端服务（back-end）
2. 排课服务（scheduler-server）
3. 前端开发服务器（本项目）

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 注意事项

1. 开发环境配置：
   - 确保 Node.js 和 pnpm 版本符合要求
   - 首次运行需要安装依赖
   - 确保后端服务已启动

2. 数据说明：
   - 首次使用需要添加基础数据
   - 时间偏好优先级：1=教师首选，2=学生首选，3=普通
   - 建议先小规模测试再实际使用

3. 构建部署：
   - 生产环境需要修改 API 地址
   - 需要配置正确的 BASE_URL
   - 建议使用 nginx 部署

## 更新日志

### v1.0.0

- 初始版本发布
- 实现基础的排课功能
- 支持时间偏好管理
- 支持课程需求管理
- 支持排课结果展示