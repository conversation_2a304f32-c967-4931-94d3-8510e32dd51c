module.exports = {
  transpileDependencies: ['@dcloudio/uni-ui', 'uview-plus'],
  configureWebpack: {
    plugins: [
      new webpack.DefinePlugin({
        'process.env': JSON.stringify({}),
        'uni': JSON.stringify(require('@dcloudio/uni-h5'))
      })
    ]
  },
  chainWebpack(config) {
    // 处理uni-app静态资源
    config.module
      .rule('media')
      .test(/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 4096,
        name: 'static/media/[name].[hash:8].[ext]'
      })
  }
}
