{"name": "course-arrangement-h5", "version": "1.0.0", "description": "Course Arrangement System H5", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-4070120250530001", "@dcloudio/uni-app-plus": " 3.0.0-alpha-4070120250530001", "@dcloudio/uni-components": "3.0.0-alpha-3000020210521001", "@dcloudio/uni-h5": "3.0.0-alpha-4070120250530001", "@dcloudio/uni-ui": "^1.4.28", "axios": "^1.6.0", "core-js": "^3.33.0", "dayjs": "^1.11.0", "pinia": "^2.1.7", "uview-plus": "^3.4.38", "vue": "^3.3.0", "@vue/composition-api": "^1.7.2", "vue-router": "^4.2.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/eslint-parser": "^7.22.0", "@babel/preset-typescript": "^7.23.0", "@dcloudio/types": "^2.0.2", "@types/node": "^20.0.0", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vue/cli-plugin-babel": "^5.0.0", "@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-plugin-typescript": "^5.0.0", "@vue/cli-service": "^5.0.0", "@vue/compiler-sfc": "^3.3.0", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.0", "babel-plugin-import": "^1.13.0", "eslint": "^7.32.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.4.0", "postcss-preset-env": "^9.0.0", "prettier": "^3.0.0", "sass": "^1.69.0", "sass-loader": "^13.3.0", "typescript": "^5.0.0", "vue-tsc": "^1.8.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}