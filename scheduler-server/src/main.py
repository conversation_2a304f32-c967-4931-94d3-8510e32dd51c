from datetime import datetime, timedelta
from typing import List, Dict, Optional
import collections
import logging
import os
from pathlib import Path

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field, conint
from ortools.sat.python import cp_model

# 配置日志
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)
logging.basicConfig(
    filename=log_dir / "scheduler.log",
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Course Scheduling Service")

# 请求模型
class Teacher(BaseModel):
    id: int
    name: str
    subjects: List[str]

class Student(BaseModel):
    id: int
    name: str

class Group(BaseModel):
    id: int
    name: str
    student_ids: List[int]
    subject: str

class CourseDemand(BaseModel):
    subject: str
    teacher_id: int
    group_ids: List[int]
    duration: conint(gt=0)  # 课程时长（分钟）

class TimePreference(BaseModel):
    entity_type: str = Field(..., pattern='^(teacher|student)$')
    entity_id: int
    day: str
    start: str  # HH:MM 格式
    end: str    # HH:MM 格式
    priority: conint(ge=1, le=3)  # 1=老师首选, 2=学生首选, 3=普通空闲

class ScheduleRequest(BaseModel):
    teachers: List[Teacher]
    students: List[Student]
    groups: List[Group]
    course_demands: List[CourseDemand]
    time_preferences: List[TimePreference]

# 响应模型
class ScheduledCourse(BaseModel):
    subject: str
    teacher: str
    group: str
    day: str
    start: str  # HH:MM 格式
    end: str    # HH:MM 格式
    student_names: List[str]
    weight: int  # 优先级权重

class UnscheduledCourse(BaseModel):
    subject: str
    teacher: str
    group: str
    student_names: List[str]
    conflict_reasons: List[str]
    suggestions: List[str]

class ScheduleResponse(BaseModel):
    status: str = Field(..., pattern='^(optimal|partial|failed)$')
    total_courses: int
    schedules: List[ScheduledCourse]
    unscheduled: List[UnscheduledCourse]

def time_to_minutes(time_str: str) -> int:
    """将HH:MM时间转换为分钟数"""
    t = datetime.strptime(time_str, "%H:%M")
    return t.hour * 60 + t.minute

def minutes_to_time(minutes: int) -> str:
    """将分钟数转换为HH:MM时间"""
    return f"{minutes//60:02d}:{minutes%60:02d}"

class SchedulingEngine:
    def __init__(self, data: ScheduleRequest):
        self.data = data
        self.model = cp_model.CpModel()
        self.solver = cp_model.CpSolver()
        self.variables = {}
        self.teacher_map = {t.id: t for t in data.teachers}
        self.student_map = {s.id: s for s in data.students}
        self.group_map = {g.id: g for g in data.groups}

    def calculate_time_weight(self, entity_type: str, entity_id: int, day: str, start_time: str) -> int:
        """计算时间点的优先级权重"""
        start_min = time_to_minutes(start_time)
        max_priority = 0

        for pref in self.data.time_preferences:
            if (pref.entity_type == entity_type and 
                pref.entity_id == entity_id and 
                pref.day == day):
                
                pref_start = time_to_minutes(pref.start)
                pref_end = time_to_minutes(pref.end)
                
                if pref_start <= start_min < pref_end:
                    if pref.priority == 1:  # 老师首选
                        return 1
                    elif pref.priority == 2:  # 学生首选
                        max_priority = max(max_priority, 2)
                    else:  # 普通空闲
                        max_priority = max(max_priority, 3)
        
        return max_priority if max_priority > 0 else 4  # 无偏好时间段

    def solve(self) -> ScheduleResponse:
        """求解排课问题"""
        try:
            # 1. 生成时间槽
            time_slots = []
            days = ["周一", "周二", "周三", "周四", "周五"]
            for day in days:
                for hour in range(8, 20):  # 8:00 - 20:00
                    for minute in range(0, 60, 15):  # 15分钟为单位
                        time_slots.append((
                            day,
                            f"{hour:02d}:{minute:02d}",
                            f"{hour:02d}:{(minute+15)%60:02d}" if minute != 45 
                            else f"{(hour+1):02d}:00"
                        ))

            # 2. 创建变量和权重
            all_assignments = []
            for demand in self.data.course_demands:
                teacher = self.teacher_map[demand.teacher_id]
                
                # 验证教师是否可以教授该课程
                if demand.subject not in teacher.subjects:
                    continue

                for (day, slot_start, _) in time_slots:
                    start_min = time_to_minutes(slot_start)
                    end_min = start_min + demand.duration
                    if end_min > 20*60:  # 超过20:00
                        continue

                    # 计算权重
                    teacher_weight = self.calculate_time_weight(
                        "teacher", teacher.id, day, slot_start)

                    student_weights = []
                    for group_id in demand.group_ids:
                        group = self.group_map[group_id]
                        for student_id in group.student_ids:
                            weight = self.calculate_time_weight(
                                "student", student_id, day, slot_start)
                            student_weights.append(weight)

                    # 取最差权重
                    group_weight = max(student_weights) if student_weights else 4
                    total_weight = teacher_weight * 100 + group_weight

                    # 创建变量
                    var = self.model.NewBoolVar(
                        f"{demand.subject}_{teacher.name}_{day}_{slot_start}")
                    self.variables[(demand, day, slot_start)] = (var, total_weight)
                    all_assignments.append(var)

            # 3. 添加约束
            # 每个需求最多安排一次
            for demand in self.data.course_demands:
                demand_vars = [
                    var for (d, _, _), (var, _) in self.variables.items()
                    if d == demand
                ]
                if demand_vars:
                    self.model.Add(sum(demand_vars) <= 1)

            # 教师时间冲突约束
            for teacher_id in self.teacher_map:
                teacher_assignments = []
                for (demand, day, start), (var, _) in self.variables.items():
                    if demand.teacher_id == teacher_id:
                        teacher_assignments.append(
                            (var, day, start, demand.duration))

                for i, (var_i, day_i, start_i, dur_i) in enumerate(teacher_assignments):
                    start_min_i = time_to_minutes(start_i)
                    end_min_i = start_min_i + dur_i

                    for var_j, day_j, start_j, dur_j in teacher_assignments[i+1:]:
                        if day_i != day_j:
                            continue

                        start_min_j = time_to_minutes(start_j)
                        end_min_j = start_min_j + dur_j

                        if not (end_min_i <= start_min_j or end_min_j <= start_min_i):
                            self.model.Add(var_i + var_j <= 1)

            # 学生时间冲突约束
            for student_id in self.student_map:
                student_assignments = []
                for (demand, day, start), (var, _) in self.variables.items():
                    for group_id in demand.group_ids:
                        group = self.group_map[group_id]
                        if student_id in group.student_ids:
                            student_assignments.append(
                                (var, day, start, demand.duration))
                            break

                for i, (var_i, day_i, start_i, dur_i) in enumerate(student_assignments):
                    start_min_i = time_to_minutes(start_i)
                    end_min_i = start_min_i + dur_i

                    for var_j, day_j, start_j, dur_j in student_assignments[i+1:]:
                        if day_i != day_j:
                            continue

                        start_min_j = time_to_minutes(start_j)
                        end_min_j = start_min_j + dur_j

                        if not (end_min_i <= start_min_j or end_min_j <= start_min_i):
                            self.model.Add(var_i + var_j <= 1)

            # 4. 设置目标：最小化总权重
            objective = sum(var * weight for _, (var, weight) in self.variables.items())
            self.model.Minimize(objective)

            # 5. 求解
            status = self.solver.Solve(self.model)

            # 6. 处理结果
            schedules = []
            unscheduled = []
            scheduled_demands = set()

            if status == cp_model.OPTIMAL or status == cp_model.FEASIBLE:
                # 收集已安排的课程
                for (demand, day, start), (var, weight) in self.variables.items():
                    if self.solver.Value(var) > 0:
                        start_min = time_to_minutes(start)
                        end_min = start_min + demand.duration
                        teacher = self.teacher_map[demand.teacher_id]

                        # 收集学生姓名
                        student_names = []
                        for group_id in demand.group_ids:
                            group = self.group_map[group_id]
                            for student_id in group.student_ids:
                                student = self.student_map[student_id]
                                student_names.append(student.name)

                        schedules.append(ScheduledCourse(
                            subject=demand.subject,
                            teacher=teacher.name,
                            group=",".join(self.group_map[gid].name 
                                         for gid in demand.group_ids),
                            day=day,
                            start=start,
                            end=minutes_to_time(end_min),
                            student_names=student_names,
                            weight=weight
                        ))
                        scheduled_demands.add(demand)

            # 处理未能安排的课程
            for demand in self.data.course_demands:
                if demand not in scheduled_demands:
                    teacher = self.teacher_map[demand.teacher_id]
                    student_names = []
                    group_names = []
                    for group_id in demand.group_ids:
                        group = self.group_map[group_id]
                        group_names.append(group.name)
                        for student_id in group.student_ids:
                            student = self.student_map[student_id]
                            student_names.append(student.name)

                    conflict_reasons = []
                    suggestions = []

                    # 分析冲突原因
                    teacher_prefs = [p for p in self.data.time_preferences
                                   if p.entity_type == "teacher" 
                                   and p.entity_id == teacher.id]
                    if not teacher_prefs:
                        conflict_reasons.append(f"教师 {teacher.name} 无可用时间")
                        suggestions.append(f"请为教师 {teacher.name} 添加可用时间")

                    student_conflicts = []
                    for student_id in set().union(*(
                        self.group_map[gid].student_ids for gid in demand.group_ids)):
                        student = self.student_map[student_id]
                        student_prefs = [p for p in self.data.time_preferences
                                       if p.entity_type == "student" 
                                       and p.entity_id == student_id]
                        if not student_prefs:
                            student_conflicts.append(student.name)

                    if student_conflicts:
                        conflict_reasons.append(
                            f"学生 {','.join(student_conflicts)} 无可用时间")
                        suggestions.append(
                            f"请为学生 {','.join(student_conflicts)} 添加可用时间")

                    if not conflict_reasons:
                        conflict_reasons.append("与其他已安排课程时间冲突")
                        suggestions.append("尝试调整教师或学生的时间偏好")

                    unscheduled.append(UnscheduledCourse(
                        subject=demand.subject,
                        teacher=teacher.name,
                        group=",".join(group_names),
                        student_names=student_names,
                        conflict_reasons=conflict_reasons,
                        suggestions=suggestions
                    ))

            return ScheduleResponse(
                status="optimal" if status == cp_model.OPTIMAL else
                       "partial" if status == cp_model.FEASIBLE else "failed",
                total_courses=len(self.data.course_demands),
                schedules=schedules,
                unscheduled=unscheduled
            )

        except Exception as e:
            logger.error(f"Scheduling error: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Scheduling failed: {str(e)}"
            )

@app.post("/schedule", response_model=ScheduleResponse)
async def schedule_courses(request: ScheduleRequest) -> ScheduleResponse:
    """
    排课接口

    接收课程需求和时间偏好，返回排课结果与冲突分析
    """
    logger.info("Received scheduling request")
    try:
        engine = SchedulingEngine(request)
        response = engine.solve()
        logger.info(
            f"Scheduling completed: {len(response.schedules)} scheduled, "
            f"{len(response.unscheduled)} unscheduled")
        return response
    except Exception as e:
        logger.error(f"Scheduling failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Scheduling failed: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)