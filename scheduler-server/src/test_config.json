{"teachers": [{"id": 1, "name": "张老师", "subjects": ["数学", "物理"]}, {"id": 2, "name": "李老师", "subjects": ["英语", "语文"]}], "students": [{"id": 1, "name": "学生1"}, {"id": 2, "name": "学生2"}, {"id": 3, "name": "学生3"}, {"id": 4, "name": "学生4"}], "groups": [{"id": 1, "name": "数学组1", "student_ids": [1, 2], "subject": "数学"}, {"id": 2, "name": "英语组1", "student_ids": [1, 3], "subject": "英语"}], "course_demands": [{"subject": "数学", "teacher_id": 1, "group_ids": [1], "duration": 45}, {"subject": "英语", "teacher_id": 2, "group_ids": [2], "duration": 45}], "time_preferences": [{"entity_type": "teacher", "entity_id": 1, "day": "周一", "start": "09:00", "end": "11:00", "priority": 1}, {"entity_type": "teacher", "entity_id": 2, "day": "周一", "start": "14:00", "end": "16:00", "priority": 1}, {"entity_type": "student", "entity_id": 1, "day": "周一", "start": "09:00", "end": "12:00", "priority": 2}, {"entity_type": "student", "entity_id": 2, "day": "周一", "start": "09:00", "end": "12:00", "priority": 2}, {"entity_type": "student", "entity_id": 3, "day": "周一", "start": "14:00", "end": "17:00", "priority": 2}]}