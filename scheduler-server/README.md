# 排课系统使用说明

## 目录结构

```
course-arrangement/
├── back-end/           # Go后端服务
│   ├── main.go          # 主程序
│   └── internal/        # 内部包
│       ├── model/       # 数据模型
│       ├── repo/        # 数据访问层
│       ├── service/     # 业务逻辑层
│       └── handler/     # HTTP处理器
└── scheduler-server/   # Python排课服务
    ├── src/
    │   ├── main.py      # FastAPI服务
    │   └── test_config.json  # 测试配置
    └── logs/            # 日志目录
```

## 启动服务

1. 启动排课服务（scheduler-server）：

```bash
cd scheduler-server
source .venv/bin/activate  # 激活虚拟环境
./start.sh                # 启动FastAPI服务
```

服务将在 http://localhost:8000 启动，可以访问 http://localhost:8000/docs 查看API文档。

2. 启动后端服务（back-end）：

```bash
cd back-end
go run main.go          # 默认使用SQLite数据库
# 或
ENV=dev go run main.go  # 使用PostgreSQL数据库
```

服务将在 http://localhost:8080 启动。

## 测试流程

1. 创建基础数据：

```bash
# 创建教师
curl -X POST http://localhost:8080/api/teachers \
  -H "Content-Type: application/json" \
  -d '{"name": "张老师", "subjects": ["数学", "物理"]}'

# 创建学生
curl -X POST http://localhost:8080/api/students \
  -H "Content-Type: application/json" \
  -d '{"name": "学生1"}'

# 创建学生组
curl -X POST http://localhost:8080/api/groups \
  -H "Content-Type: application/json" \
  -d '{"name": "数学组1", "subject": "数学", "student_ids": [1, 2]}'
```

2. 创建排课任务：

```bash
# 创建任务
curl -X POST http://localhost:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{"name": "第一周排课"}'

# 添加教师时间偏好
curl -X POST http://localhost:8080/api/tasks/1/preferences/teachers/1 \
  -H "Content-Type: application/json" \
  -d '{
    "day": "周一",
    "start_time": "09:00",
    "end_time": "11:00",
    "priority": 1
  }'

# 添加学生时间偏好
curl -X POST http://localhost:8080/api/tasks/1/preferences/students/1 \
  -H "Content-Type: application/json" \
  -d '{
    "day": "周一",
    "start_time": "09:00",
    "end_time": "12:00",
    "priority": 2
  }'

# 添加课程需求
curl -X POST http://localhost:8080/api/tasks/1/group-teacher \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": 1,
    "teacher_id": 1,
    "subject": "数学",
    "duration": 45
  }'
```

3. 执行排课：

```bash
# 运行排课
curl -X POST http://localhost:8080/api/tasks/1/run

# 查看结果
curl http://localhost:8080/api/tasks/1/schedules

# 查看未排课程和冲突
curl http://localhost:8080/api/tasks/1/unscheduled
```

## 配置说明

1. 后端服务配置（conf/config.local.yml）：
- 数据库设置（SQLite/PostgreSQL）
- 服务器端口
- 日志级别

2. 排课服务配置：
- 通过环境变量或启动参数配置
- 参考 src/test_config.json 了解数据格式

## 注意事项

1. 时间偏好优先级：
   - 1：老师首选时间
   - 2：学生首选时间
   - 3：普通空闲时间

2. 排课结果：
   - 优先满足教师时间偏好
   - 其次满足学生时间偏好
   - 输出未能排课的原因和建议

3. 日志查看：
   - 后端日志：标准输出
   - 排课服务日志：logs/scheduler.log

4. 测试数据：
   - 可使用 src/test_config.json 中的示例数据进行测试
   - 建议先用小规模数据测试系统运行情况

## 常见问题

1. 数据库连接失败
   - 检查数据库配置
   - 确保数据目录有写入权限

2. 排课服务未响应
   - 检查 8000 端口是否被占用
   - 查看 logs/scheduler.log 排查问题

3. 排课结果不理想
   - 检查时间偏好设置
   - 查看未排课原因，调整相关配置