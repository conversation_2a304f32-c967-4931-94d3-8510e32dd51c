[project]
name = "scheduler-server"
version = "1.0.0"
description = "Course Scheduling Service"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "ortools>=9.7.0",
    "pydantic>=2.4.0",
    "python-json-logger>=2.0.0",
]
requires-python = ">=3.8"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.hatch.metadata]
allow-direct-references = true

[tool.rye]
managed = true
dev-dependencies = [
    "pytest>=7.3.1",
]