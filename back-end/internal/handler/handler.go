package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/txzeyi/course-arrangement/internal/service"
)

// Handler HTTP请求处理器
type Handler struct {
	service *service.Service
}

// NewHandler 创建新的Handler实例
func NewHandler(svc *service.Service) *Handler {
	return &Handler{service: svc}
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// CreateTeacherRequest 创建教师请求
type CreateTeacherRequest struct {
	Name     string   `json:"name" binding:"required"`
	Subjects []string `json:"subjects" binding:"required,min=1"`
}

// CreateStudentRequest 创建学生请求
type CreateStudentRequest struct {
	Name string `json:"name" binding:"required"`
}

// CreateGroupRequest 创建组请求
type CreateGroupRequest struct {
	Name       string `json:"name" binding:"required"`
	Subject    string `json:"subject" binding:"required"`
	StudentIDs []uint `json:"student_ids"`
}

// CreateScheduleTaskRequest 创建排课任务请求
type CreateScheduleTaskRequest struct {
	Name string `json:"name" binding:"required"`
}

// AddTimePreferenceRequest 添加时间偏好请求
type AddTimePreferenceRequest struct {
	Day       string `json:"day" binding:"required"`
	StartTime string `json:"start_time" binding:"required"` // 格式: "15:04"
	EndTime   string `json:"end_time" binding:"required"`   // 格式: "15:04"
	Priority  int    `json:"priority" binding:"required,min=1,max=3"`
}

// AddGroupTeacherRequest 添加组-教师配对请求
type AddGroupTeacherRequest struct {
	GroupID   uint   `json:"group_id" binding:"required"`
	TeacherID uint   `json:"teacher_id" binding:"required"`
	Subject   string `json:"subject" binding:"required"`
	Duration  uint   `json:"duration" binding:"required,min=1"`
}

// CreateTeacher 创建教师
func (h *Handler) CreateTeacher(c *gin.Context) {
	var req CreateTeacherRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	teacher, err := h.service.CreateTeacher(req.Name, req.Subjects)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to create teacher: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    teacher,
	})
}

// CreateStudent 创建学生
func (h *Handler) CreateStudent(c *gin.Context) {
	var req CreateStudentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	student, err := h.service.CreateStudent(req.Name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to create student: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    student,
	})
}

// CreateGroup 创建组
func (h *Handler) CreateGroup(c *gin.Context) {
	var req CreateGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	group, err := h.service.CreateGroup(req.Name, req.Subject, req.StudentIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to create group: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    group,
	})
}

// CreateScheduleTask 创建排课任务
func (h *Handler) CreateScheduleTask(c *gin.Context) {
	var req CreateScheduleTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	task, err := h.service.CreateScheduleTask(req.Name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to create schedule task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    task,
	})
}

// AddTimePreference 添加时间偏好
func (h *Handler) AddTimePreference(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("taskId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid task ID",
		})
		return
	}

	entityType := c.Param("entityType") // "teacher" 或 "student"
	if entityType != "teacher" && entityType != "student" {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid entity type",
		})
		return
	}

	entityID, err := strconv.ParseUint(c.Param("entityId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid entity ID",
		})
		return
	}

	var req AddTimePreferenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	startTime, err := time.Parse("15:04", req.StartTime)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid start time format",
		})
		return
	}

	endTime, err := time.Parse("15:04", req.EndTime)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid end time format",
		})
		return
	}

	err = h.service.AddTimePreference(
		uint(taskID),
		entityType,
		uint(entityID),
		req.Day,
		startTime,
		endTime,
		req.Priority,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to add time preference: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
	})
}

// AddGroupTeacher 添加组-教师配对
func (h *Handler) AddGroupTeacher(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("taskId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid task ID",
		})
		return
	}

	var req AddGroupTeacherRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	err = h.service.AddGroupTeacher(
		uint(taskID),
		req.GroupID,
		req.TeacherID,
		req.Subject,
		req.Duration,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to add group-teacher pair: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
	})
}

// RunScheduling 执行排课
func (h *Handler) RunScheduling(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("taskId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid task ID",
		})
		return
	}

	err = h.service.RunScheduling(uint(taskID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to run scheduling: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
	})
}

// GetTaskSchedules 获取任务的排课结果
func (h *Handler) GetTaskSchedules(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("taskId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid task ID",
		})
		return
	}

	schedules, err := h.service.GetTaskSchedules(uint(taskID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to get schedules: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    schedules,
	})
}

// GetTaskUnscheduledCourses 获取任务未能排课的课程
func (h *Handler) GetTaskUnscheduledCourses(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("taskId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid task ID",
		})
		return
	}

	courses, err := h.service.GetTaskUnscheduledCourses(uint(taskID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to get unscheduled courses: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    courses,
	})
}

// ListTeachers 获取所有教师
func (h *Handler) ListTeachers(c *gin.Context) {
	teachers, err := h.service.ListTeachers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to get teachers: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    teachers,
	})
}

// ListGroups 获取所有组
func (h *Handler) ListGroups(c *gin.Context) {
	groups, err := h.service.ListGroups()
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to get groups: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    groups,
	})
}

// ListStudents 获取所有学生
func (h *Handler) ListStudents(c *gin.Context) {
	students, err := h.service.ListStudents()
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to get students: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    students,
	})
}

// GetTaskStatus 获取任务状态
func (h *Handler) GetTaskStatus(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("taskId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    400,
			Message: "Invalid task ID",
		})
		return
	}

	task, err := h.service.GetTaskByID(uint(taskID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to get task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    task,
	})
}