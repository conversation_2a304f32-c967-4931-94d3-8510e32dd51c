package model

import (
	"time"

	"gorm.io/gorm"
)

// Teacher 老师表
type Teacher struct {
	ID       uint     `gorm:"primarykey"`
	Name     string   `gorm:"type:varchar(100);not null"`
	Subjects []string `gorm:"type:text[];not null"` // PostgreSQL 数组类型
}

// Student 学生表
type Student struct {
	ID   uint   `gorm:"primarykey"`
	Name string `gorm:"type:varchar(100);not null"`
}

// Group 学生组表
type Group struct {
	ID       uint      `gorm:"primarykey"`
	Name     string    `gorm:"type:varchar(100);not null"`
	Subject  string    `gorm:"type:varchar(100);not null"`
	Students []Student `gorm:"many2many:group_student;"`
}

// ScheduleTask 排课任务表
type ScheduleTask struct {
	ID        uint      `gorm:"primarykey"`
	Name      string    `gorm:"type:varchar(100);not null"`
	Status    string    `gorm:"type:varchar(20);not null;default:'pending'"` // pending, processing, completed, failed
	CreatedAt time.Time `gorm:"not null"`
}

// TimePreference 时间偏好表
type TimePreference struct {
	ID         uint      `gorm:"primarykey"`
	TaskID     uint      `gorm:"not null"`
	EntityType string    `gorm:"type:varchar(20);not null"` // teacher 或 student
	EntityID   uint      `gorm:"not null"`
	Day        string    `gorm:"type:varchar(20);not null"` // 周一到周日
	StartTime  time.Time `gorm:"not null;type:time"`
	EndTime    time.Time `gorm:"not null;type:time"`
	Priority   int       `gorm:"not null"` // 1=老师首选, 2=学生首选, 3=普通空闲

	Task ScheduleTask `gorm:"foreignKey:TaskID"`
}

// GroupTeacher 组-老师配对表（课程需求）
type GroupTeacher struct {
	ID        uint   `gorm:"primarykey"`
	TaskID    uint   `gorm:"not null"`
	GroupID   uint   `gorm:"not null"`
	TeacherID uint   `gorm:"not null"`
	Duration  uint   `gorm:"not null"` // 课程时长（分钟）
	Subject   string `gorm:"type:varchar(100);not null"`

	Task    ScheduleTask `gorm:"foreignKey:TaskID"`
	Group   Group        `gorm:"foreignKey:GroupID"`
	Teacher Teacher      `gorm:"foreignKey:TeacherID"`
}

// Schedule 排课结果表
type Schedule struct {
	ID        uint      `gorm:"primarykey"`
	TaskID    uint      `gorm:"not null"`
	GroupID   uint      `gorm:"not null"`
	TeacherID uint      `gorm:"not null"`
	Subject   string    `gorm:"type:varchar(100);not null"`
	Day       string    `gorm:"type:varchar(20);not null"`
	StartTime time.Time `gorm:"not null;type:time"`
	EndTime   time.Time `gorm:"not null;type:time"`
	Weight    int       `gorm:"not null"` // 权重（优先级）

	Task    ScheduleTask `gorm:"foreignKey:TaskID"`
	Group   Group        `gorm:"foreignKey:GroupID"`
	Teacher Teacher      `gorm:"foreignKey:TeacherID"`
}

// UnscheduledCourse 未能排课的课程及原因
type UnscheduledCourse struct {
	ID        uint   `gorm:"primarykey"`
	TaskID    uint   `gorm:"not null"`
	GroupID   uint   `gorm:"not null"`
	TeacherID uint   `gorm:"not null"`
	Subject   string `gorm:"type:varchar(100);not null"`
	Reasons   string `gorm:"type:text[]"` // 冲突原因
	Solutions string `gorm:"type:text[]"` // 建议解决方案

	Task    ScheduleTask `gorm:"foreignKey:TaskID"`
	Group   Group        `gorm:"foreignKey:GroupID"`
	Teacher Teacher      `gorm:"foreignKey:TeacherID"`
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&Teacher{},
		&Student{},
		&Group{},
		&ScheduleTask{},
		&TimePreference{},
		&GroupTeacher{},
		&Schedule{},
		&UnscheduledCourse{},
	)
}