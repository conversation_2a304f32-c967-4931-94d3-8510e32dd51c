package service

import (
	"encoding/json"
	"fmt"
	"os/exec"
	"path/filepath"

	"github.com/txzeyi/course-arrangement/internal/model"
	"github.com/txzeyi/course-arrangement/internal/repo"
)

// SchedulingService 排课服务
type SchedulingService struct {
	repo *repo.Repository
}

// NewSchedulingService 创建排课服务实例
func NewSchedulingService(repository *repo.Repository) *SchedulingService {
	return &SchedulingService{repo: repository}
}

// RunScheduling 执行排课
func (s *SchedulingService) RunScheduling(taskID uint) error {
	// 1. 获取任务信息
	task, err := s.repo.GetTaskByID(taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// 验证任务状态
	if task.Status != "pending" {
		return fmt.Errorf("task status is not pending: %s", task.Status)
	}

	// 2. 更新任务状态为进行中
	err = s.repo.UpdateTaskStatus(taskID, "processing")
	if err != nil {
		return fmt.Errorf("failed to update task status: %w", err)
	}

	// 3. 准备排课数据
	// 3.1 获取时间偏好
	preferences, err := s.repo.GetTaskTimePreferences(taskID)
	if err != nil {
		s.repo.UpdateTaskStatus(taskID, "failed")
		return fmt.Errorf("failed to get preferences: %w", err)
	}

	// 3.2 获取教师时间偏好
	teacherPrefs := make(map[uint][]model.TimePreference)
	for _, pref := range preferences {
		if pref.EntityType == "teacher" {
			teacherPrefs[pref.EntityID] = append(teacherPrefs[pref.EntityID], pref)
		}
	}

	// 3.3 获取学生时间偏好
	studentPrefs := make(map[uint][]model.TimePreference)
	for _, pref := range preferences {
		if pref.EntityType == "student" {
			studentPrefs[pref.EntityID] = append(studentPrefs[pref.EntityID], pref)
		}
	}

	// 3.4 获取课程需求
	groupTeachers, err := s.repo.GetTaskGroupTeachers(taskID)
	if err != nil {
		s.repo.UpdateTaskStatus(taskID, "failed")
		return fmt.Errorf("failed to get course demands: %w", err)
	}

	// 4. 构建排课请求
	request := map[string]interface{}{
		"task_id":          taskID,
		"teacher_prefs":    teacherPrefs,
		"student_prefs":    studentPrefs,
		"group_teachers":   groupTeachers,
	}

	// 5. 调用Python排课服务
	requestJSON, err := json.Marshal(request)
	if err != nil {
		s.repo.UpdateTaskStatus(taskID, "failed")
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	scriptPath := filepath.Join("script", "schedule.py")
	cmd := exec.Command("python3", scriptPath, string(requestJSON))
	output, err := cmd.CombinedOutput()
	if err != nil {
		s.repo.UpdateTaskStatus(taskID, "failed")
		return fmt.Errorf("failed to run scheduling: %w", err)
	}

	// 6. 解析排课结果
	var result struct {
		Status      string          `json:"status"`
		Schedules   []model.Schedule `json:"schedules"`
		Unscheduled []model.UnscheduledCourse `json:"unscheduled"`
	}

	err = json.Unmarshal(output, &result)
	if err != nil {
		s.repo.UpdateTaskStatus(taskID, "failed")
		return fmt.Errorf("failed to unmarshal result: %w", err)
	}

	// 7. 保存排课结果
	for _, schedule := range result.Schedules {
		schedule.TaskID = taskID
		err = s.repo.SaveSchedule(&schedule)
		if err != nil {
			s.repo.UpdateTaskStatus(taskID, "failed")
			return fmt.Errorf("failed to save schedule: %w", err)
		}
	}

	for _, unscheduled := range result.Unscheduled {
		unscheduled.TaskID = taskID
		err = s.repo.SaveUnscheduledCourse(&unscheduled)
		if err != nil {
			s.repo.UpdateTaskStatus(taskID, "failed")
			return fmt.Errorf("failed to save unscheduled course: %w", err)
		}
	}

	// 8. 更新任务状态
	status := "completed"
	if len(result.Unscheduled) > 0 {
		status = "partial"
	}
	return s.repo.UpdateTaskStatus(taskID, status)
}