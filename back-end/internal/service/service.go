package service

import (
	"errors"
	"time"

	"github.com/txzeyi/course-arrangement/internal/model"
	"github.com/txzeyi/course-arrangement/internal/repo"
)

// Service 服务层结构
type Service struct {
	repo      *repo.Repository
	scheduler *SchedulingService
}

// NewService 创建新的服务实例
func NewService(repo *repo.Repository) *Service {
	return &Service{
		repo:      repo,
		scheduler: NewSchedulingService(repo),
	}
}

// CreateTeacher 创建教师
func (s *Service) CreateTeacher(name string, subjects []string) (*model.Teacher, error) {
	if name == "" {
		return nil, errors.New("teacher name cannot be empty")
	}
	if len(subjects) == 0 {
		return nil, errors.New("teacher must teach at least one subject")
	}
	
	teacher := &model.Teacher{
		Name:     name,
		Subjects: subjects,
	}
	if err := s.repo.CreateTeacher(teacher); err != nil {
		return nil, err
	}
	return teacher, nil
}

// CreateStudent 创建学生
func (s *Service) CreateStudent(name string) (*model.Student, error) {
	if name == "" {
		return nil, errors.New("student name cannot be empty")
	}
	
	student := &model.Student{Name: name}
	if err := s.repo.CreateStudent(student); err != nil {
		return nil, err
	}
	return student, nil
}

// CreateGroup 创建学生组
func (s *Service) CreateGroup(name string, subject string, studentIDs []uint) (*model.Group, error) {
	if name == "" {
		return nil, errors.New("group name cannot be empty")
	}
	if subject == "" {
		return nil, errors.New("group subject cannot be empty")
	}

	// 创建组
	group := &model.Group{
		Name:    name,
		Subject: subject,
	}

	// 如果提供了学生ID，查询这些学生并关联
	if len(studentIDs) > 0 {
		students, err := s.repo.ListStudents()
		if err != nil {
			return nil, err
		}

		// 验证所有学生ID是否有效
		studentMap := make(map[uint]model.Student)
		for _, student := range students {
			studentMap[student.ID] = student
		}

		for _, id := range studentIDs {
			if _, ok := studentMap[id]; !ok {
				return nil, errors.New("invalid student ID")
			}
			group.Students = append(group.Students, studentMap[id])
		}
	}

	if err := s.repo.CreateGroup(group); err != nil {
		return nil, err
	}
	return group, nil
}

// CreateScheduleTask 创建排课任务
func (s *Service) CreateScheduleTask(name string) (*model.ScheduleTask, error) {
	if name == "" {
		return nil, errors.New("task name cannot be empty")
	}

	task := &model.ScheduleTask{
		Name:      name,
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	if err := s.repo.CreateScheduleTask(task); err != nil {
		return nil, err
	}
	return task, nil
}

// AddTimePreference 添加时间偏好
func (s *Service) AddTimePreference(taskID uint, entityType string, entityID uint, 
	day string, startTime, endTime time.Time, priority int) error {
	
	if entityType != "teacher" && entityType != "student" {
		return errors.New("invalid entity type")
	}
	if priority < 1 || priority > 3 {
		return errors.New("invalid priority")
	}

	pref := &model.TimePreference{
		TaskID:     taskID,
		EntityType: entityType,
		EntityID:   entityID,
		Day:        day,
		StartTime:  startTime,
		EndTime:    endTime,
		Priority:   priority,
	}
	return s.repo.AddTimePreference(pref)
}

// AddGroupTeacher 添加组-教师配对（课程需求）
func (s *Service) AddGroupTeacher(taskID uint, groupID uint, teacherID uint, 
	subject string, duration uint) error {
	
	gt := &model.GroupTeacher{
		TaskID:    taskID,
		GroupID:   groupID,
		TeacherID: teacherID,
		Subject:   subject,
		Duration:  duration,
	}
	return s.repo.AddGroupTeacher(gt)
}

// SaveUnscheduledCourse 保存未能排课的课程
func (s *Service) SaveUnscheduledCourse(course *model.UnscheduledCourse) error {
	return s.repo.SaveUnscheduledCourse(course)
}

// RunScheduling 执行排课
func (s *Service) RunScheduling(taskID uint) error {
	return s.scheduler.RunScheduling(taskID)
}

// GetTaskSchedules 获取任务的排课结果
func (s *Service) GetTaskSchedules(taskID uint) ([]model.Schedule, error) {
	return s.repo.GetTaskSchedules(taskID)
}

// GetTaskUnscheduledCourses 获取任务未能排课的课程
func (s *Service) GetTaskUnscheduledCourses(taskID uint) ([]model.UnscheduledCourse, error) {
	return s.repo.GetTaskUnscheduledCourses(taskID)
}

// GetTaskTimePreferences 获取任务的时间偏好
func (s *Service) GetTaskTimePreferences(taskID uint) ([]model.TimePreference, error) {
	return s.repo.GetTaskTimePreferences(taskID)
}

// ListTeachers 获取所有教师
func (s *Service) ListTeachers() ([]model.Teacher, error) {
	return s.repo.ListTeachers()
}

// ListStudents 获取所有学生
func (s *Service) ListStudents() ([]model.Student, error) {
	return s.repo.ListStudents()
}

// ListGroups 获取所有组
func (s *Service) ListGroups() ([]model.Group, error) {
	return s.repo.ListGroups()
}

// GetTaskByID 获取任务详情
func (s *Service) GetTaskByID(taskID uint) (*model.ScheduleTask, error) {
	return s.repo.GetTaskByID(taskID)
}