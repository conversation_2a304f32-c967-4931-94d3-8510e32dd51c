package repo

import (
	"github.com/txzeyi/course-arrangement/internal/model"
	"gorm.io/gorm"
)

// Repository 定义数据库操作接口
type Repository struct {
	db *gorm.DB
}

// NewRepository 创建新的 Repository 实例
func NewRepository(db *gorm.DB) *Repository {
	return &Repository{db: db}
}

// CreateTeacher 创建老师
func (r *Repository) CreateTeacher(teacher *model.Teacher) error {
	return r.db.Create(teacher).Error
}

// CreateStudent 创建学生
func (r *Repository) CreateStudent(student *model.Student) error {
	return r.db.Create(student).Error
}

// CreateGroup 创建学生组
func (r *Repository) CreateGroup(group *model.Group) error {
	return r.db.Create(group).Error
}

// CreateScheduleTask 创建排课任务
func (r *Repository) CreateScheduleTask(task *model.ScheduleTask) error {
	return r.db.Create(task).Error
}

// AddTimePreference 添加时间偏好
func (r *Repository) AddTimePreference(pref *model.TimePreference) error {
	return r.db.Create(pref).Error
}

// AddGroupTeacher 添加组-老师配对
func (r *Repository) AddGroupTeacher(groupTeacher *model.GroupTeacher) error {
	return r.db.Create(groupTeacher).Error
}

// SaveSchedule 保存排课结果
func (r *Repository) SaveSchedule(schedule *model.Schedule) error {
	return r.db.Create(schedule).Error
}

// SaveUnscheduledCourse 保存未能排课的课程
func (r *Repository) SaveUnscheduledCourse(course *model.UnscheduledCourse) error {
	return r.db.Create(course).Error
}

// GetTaskByID 获取排课任务
func (r *Repository) GetTaskByID(id uint) (*model.ScheduleTask, error) {
	var task model.ScheduleTask
	err := r.db.First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetTaskTimePreferences 获取任务的时间偏好
func (r *Repository) GetTaskTimePreferences(taskID uint) ([]model.TimePreference, error) {
	var prefs []model.TimePreference
	err := r.db.Where("task_id = ?", taskID).Find(&prefs).Error
	return prefs, err
}

// GetTaskTeacherTimePreferences 获取任务的教师时间偏好
func (r *Repository) GetTaskTeacherTimePreferences(taskID uint) ([]model.TimePreference, error) {
	var prefs []model.TimePreference
	err := r.db.Where("task_id = ? AND entity_type = ?", taskID, "teacher").Find(&prefs).Error
	return prefs, err
}

// GetTaskStudentTimePreferences 获取任务的学生时间偏好
func (r *Repository) GetTaskStudentTimePreferences(taskID uint) ([]model.TimePreference, error) {
	var prefs []model.TimePreference
	err := r.db.Where("task_id = ? AND entity_type = ?", taskID, "student").Find(&prefs).Error
	return prefs, err
}

// GetTaskGroupTeachers 获取任务的组-老师配对
func (r *Repository) GetTaskGroupTeachers(taskID uint) ([]model.GroupTeacher, error) {
	var pairs []model.GroupTeacher
	err := r.db.Where("task_id = ?", taskID).Find(&pairs).Error
	return pairs, err
}

// GetTaskSchedules 获取任务的排课结果
func (r *Repository) GetTaskSchedules(taskID uint) ([]model.Schedule, error) {
	var schedules []model.Schedule
	err := r.db.Where("task_id = ?", taskID).Find(&schedules).Error
	return schedules, err
}

// GetTaskUnscheduledCourses 获取任务未能排课的课程
func (r *Repository) GetTaskUnscheduledCourses(taskID uint) ([]model.UnscheduledCourse, error) {
	var courses []model.UnscheduledCourse
	err := r.db.Where("task_id = ?", taskID).Find(&courses).Error
	return courses, err
}

// UpdateTaskStatus 更新任务状态
func (r *Repository) UpdateTaskStatus(taskID uint, status string) error {
	return r.db.Model(&model.ScheduleTask{}).Where("id = ?", taskID).Update("status", status).Error
}

// ListTeachers 获取所有老师
func (r *Repository) ListTeachers() ([]model.Teacher, error) {
	var teachers []model.Teacher
	err := r.db.Find(&teachers).Error
	return teachers, err
}

// ListGroups 获取所有组
func (r *Repository) ListGroups() ([]model.Group, error) {
	var groups []model.Group
	err := r.db.Find(&groups).Error
	return groups, err
}

// ListStudents 获取所有学生
func (r *Repository) ListStudents() ([]model.Student, error) {
	var students []model.Student
	err := r.db.Find(&students).Error
	return students, err
}