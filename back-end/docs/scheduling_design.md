# 排课系统接口与数据结构设计

## 1. 总体架构

- Go后端负责数据建模、API、任务管理、调用排课服务。
- Python FastAPI服务负责排课算法、冲突分析，暴露 `/schedule` 接口。

## 2. 数据结构定义

### 2.1 教师（Teacher）

```json
{
  "id": "int",
  "name": "string",
  "subjects": ["string"]
}
```

### 2.2 学生（Student）

```json
{
  "id": "int",
  "name": "string"
}
```

### 2.3 学生组（Group）

```json
{
  "id": "int",
  "name": "string",
  "student_ids": ["int"],
  "subject": "string"
}
```

### 2.4 课程需求（CourseDemand）

```json
{
  "subject": "string",
  "teacher_id": "int",
  "group_ids": ["int"],
  "duration": "int" // 单位: 分钟
}
```

### 2.5 时间偏好（TimePreference）

```json
{
  "entity_type": "string", // "teacher" 或 "student"
  "entity_id": "int",
  "day": "string", // 如 "周一"
  "start": "string", // "HH:MM"
  "end": "string",   // "HH:MM"
  "priority": "int"  // 1=老师首选, 2=学生首选, 3=普通空闲
}
```

## 3. /schedule 接口定义

### 3.1 请求体（Request）

```json
{
  "teachers": [Teacher],
  "students": [Student],
  "groups": [Group],
  "course_demands": [CourseDemand],
  "time_preferences": [TimePreference]
}
```

### 3.2 响应体（Response）

```json
{
  "status": "string", // "optimal" | "partial" | "failed"
  "total_courses": "int",
  "schedules": [
    {
      "subject": "string",
      "teacher": "string",
      "group": "string",
      "day": "string",
      "start": "string",
      "end": "string",
      "student_names": ["string"],
      "weight": "int"
    }
  ],
  "unscheduled": [
    {
      "subject": "string",
      "teacher": "string",
      "group": "string",
      "student_names": ["string"],
      "conflict_reasons": ["string"],
      "suggestions": ["string"]
    }
  ]
}
```

## 4. 排课优先级与冲突处理

- 优先级顺序：老师首选 > 学生首选 > 普通空闲 > 调整学生 > 调整老师 > 无法排课。
- 冲突输出：未能排课的课程，输出冲突原因（如老师无空闲、学生组无共同空闲、与其他课程冲突等），并给出可行建议（如调整学生/老师时间）。

## 5. 主要流程

```mermaid
flowchart TD
    A[Go后端收集数据] --> B[组装排课请求]
    B --> C[POST /schedule 到Python服务]
    C --> D[Python服务排课+冲突分析]
    D --> E[返回预排结果+冲突说明]
    E --> F[Go后端解析并存储/返回]
```

## 6. 示例

### 6.1 请求示例

```json
{
  "teachers": [
    {"id": 1, "name": "张老师", "subjects": ["数学", "物理"]}
  ],
  "students": [
    {"id": 1, "name": "学生1"},
    {"id": 2, "name": "学生2"}
  ],
  "groups": [
    {"id": 1, "name": "组A", "student_ids": [1,2], "subject": "数学"}
  ],
  "course_demands": [
    {"subject": "数学", "teacher_id": 1, "group_ids": [1], "duration": 60}
  ],
  "time_preferences": [
    {"entity_type": "teacher", "entity_id": 1, "day": "周一", "start": "14:00", "end": "16:00", "priority": 1},
    {"entity_type": "student", "entity_id": 1, "day": "周一", "start": "15:00", "end": "17:00", "priority": 2}
  ]
}
```

### 6.2 响应示例

```json
{
  "status": "optimal",
  "total_courses": 1,
  "schedules": [
    {
      "subject": "数学",
      "teacher": "张老师",
      "group": "组A",
      "day": "周一",
      "start": "15:00",
      "end": "16:00",
      "student_names": ["学生1", "学生2"],
      "weight": 102
    }
  ],
  "unscheduled": []
}
```

---

## 7. 未来扩展建议

- 支持更多约束（如教室、设备等）。
- 支持多轮优化与人工干预。
- 支持多学期、跨校区排课。