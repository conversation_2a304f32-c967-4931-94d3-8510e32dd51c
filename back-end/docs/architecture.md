# 后端项目初始化方案

## 1. 目录结构设计

```mermaid
graph TD
    A[back-end/] --> B(main.go)
    A --> C(go.mod/go.sum)
    A --> D(conf/)
    D --> E(config.dev.yml)
    D --> F(config.prod.yml)
    A --> G(internal/)
    G --> H(handler/)
    G --> I(model/)
    G --> J(service/)
    G --> K(repo/)
    A --> L(pkg/)
    A --> M(script/)
    M --> N(schedule.py)
    A --> O(docs/)
    A --> P(test/)
```

## 2. 配置管理

- 使用 viper 读取 `conf/config.{env}.yml`，支持多环境。
- 配置内容包括数据库、Redis、Python 脚本路径等。

## 3. 数据库表结构设计（PostgreSQL）

### 3.1 基础表（全局）

#### teacher（老师表）
| 字段名      | 类型         | 说明     |
| ----------- | ------------| -------- |
| id          | serial PK   | 主键     |
| name        | varchar     | 姓名     |

#### student（学生表）
| 字段名      | 类型         | 说明     |
| ----------- | ------------| -------- |
| id          | serial PK   | 主键     |
| name        | varchar     | 姓名     |

#### group（学生组表）
| 字段名      | 类型         | 说明     |
| ----------- | ------------| -------- |
| id          | serial PK   | 主键     |
| name        | varchar     | 组名     |

#### group_student（组-学生关联表）
| 字段名      | 类型         | 说明     |
| ----------- | ------------| -------- |
| group_id    | int FK      | 组ID     |
| student_id  | int FK      | 学生ID   |

### 3.2 排课任务相关表（与 task_id 关联）

#### schedule_task（排课任务表）
| 字段名      | 类型         | 说明         |
| ----------- | ------------| ------------|
| id          | serial PK   | 主键        |
| name        | varchar     | 任务名称    |
| created_at  | timestamp   | 创建时间    |
| status      | varchar     | 状态（可选）|

#### teacher_free_time（老师空闲时间表）
| 字段名      | 类型         | 说明         |
| ----------- | ------------| ------------|
| id          | serial PK   | 主键        |
| task_id     | int FK      | 排课任务ID  |
| teacher_id  | int FK      | 老师ID      |
| start_time  | time        | 开始时间    |
| end_time    | time        | 结束时间    |

#### group_free_time（组空闲时间表）
| 字段名      | 类型         | 说明         |
| ----------- | ------------| ------------|
| id          | serial PK   | 主键        |
| task_id     | int FK      | 排课任务ID  |
| group_id    | int FK      | 组ID        |
| start_time  | time        | 开始时间    |
| end_time    | time        | 结束时间    |

#### group_teacher（组-老师配对表）
| 字段名      | 类型         | 说明         |
| ----------- | ------------| ------------|
| id          | serial PK   | 主键        |
| task_id     | int FK      | 排课任务ID  |
| group_id    | int FK      | 组ID        |
| teacher_id  | int FK      | 老师ID      |

#### schedule（排课结果表）
| 字段名      | 类型         | 说明         |
| ----------- | ------------| ------------|
| id          | serial PK   | 主键        |
| task_id     | int FK      | 排课任务ID  |
| group_id    | int FK      | 组ID        |
| teacher_id  | int FK      | 老师ID      |
| start_time  | timestamp   | 上课开始    |
| end_time    | timestamp   | 上课结束    |

## 4. Python 脚本集成

- Python 排课脚本放在 `script/schedule.py`。
- Go 通过 `os/exec` 调用 Python 脚本，传递参数（如 JSON 文件或命令行参数），解析脚本输出（建议输出 JSON）。
- 脚本输入输出格式需约定。

## 5. 主要模块说明

- handler：Gin 路由与 API 入口
- model：Gorm 数据模型
- service：业务逻辑
- repo：数据访问层
- script：Python 脚本
- conf：配置文件
- pkg：通用工具包
- test：单元测试

## 6. 关键流程

1. 创建排课任务（schedule_task），录入本次任务的老师、学生、组、空闲时间、配对关系。
2. 触发排课时，Go 收集本 task_id 下的所有数据，调用 Python 脚本，生成排课结果。
3. 排课结果写入 schedule 表，带 task_id 字段。
4. 可查询每次排课任务的历史数据及结果。