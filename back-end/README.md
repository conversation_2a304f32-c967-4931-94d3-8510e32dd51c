# 排课系统后端

## 项目结构

```
back-end/
├── conf/               # 配置文件
│   ├── config.local.yml  # 本地配置（SQLite）
│   └── config.dev.yml    # 开发环境配置（PostgreSQL）
├── data/              # 数据目录（SQLite数据库文件）
├── docs/              # 文档
│   ├── api.md        # API文档
│   └── architecture.md # 架构设计文档
├── internal/          # 内部包
│   ├── handler/      # HTTP处理器
│   ├── model/        # 数据模型
│   ├── repo/         # 数据访问层
│   └── service/      # 业务逻辑层
├── pkg/              # 公共包
├── script/           # 脚本
│   └── schedule.py   # 排课算法
└── main.go           # 主入口
```

## 技术栈

- Go 1.20+
- SQLite 或 PostgreSQL
- Redis (可选)
- Python 3.x
  - ortools（排课算法依赖）

## 环境变量

- `ENV`: 环境名称，决定使用的配置文件
  - `local`: 使用 SQLite（默认）
  - `dev`: 使用 PostgreSQL
  - 示例：`ENV=dev go run main.go`

## 依赖安装

1. Go 依赖：
```bash
go mod tidy
```

2. Python 依赖：
```bash
pip install ortools
```

3. 数据库：
- 本地环境（SQLite）：无需额外安装
- 开发环境（PostgreSQL）：
  ```sql
  CREATE DATABASE course_arrangement;
  ```

4. Redis（可选）：
- 安装 Redis 服务器
- 本地环境可禁用 Redis

## 配置

### 本地环境（SQLite）

修改 `conf/config.local.yml` 文件：

```yaml
server:
  port: 8080
  mode: debug

database:
  driver: sqlite
  dsn: "./data/course_arrangement.db"

redis:
  enabled: false

python:
  script_path: ./script/schedule.py
  python_path: python3
```

### 开发环境（PostgreSQL）

修改 `conf/config.dev.yml` 文件：

```yaml
server:
  port: 8080
  mode: debug

database:
  driver: postgres
  host: localhost
  port: 5432
  user: postgres
  password: postgres
  dbname: course_arrangement
  sslmode: disable

redis:
  enabled: true
  host: localhost
  port: 6379
  password: ""
  db: 0

python:
  script_path: ./script/schedule.py
  python_path: python3
```

## 运行

1. 本地环境启动（使用 SQLite）：
```bash
go run main.go
```

2. 开发环境启动（使用 PostgreSQL）：
```bash
ENV=dev go run main.go
```

3. 验证服务：
```bash
curl http://localhost:8080/api/ping
```

## API 使用

详见 [API文档](docs/api.md)

## 排课流程

1. 创建基础数据
   - 创建教师
   - 创建学生
   - 创建学生组

2. 创建排课任务
   - 创建任务
   - 添加教师空闲时间
   - 添加组空闲时间
   - 添加组-教师配对关系

3. 执行排课
   - 调用排课接口
   - 查询排课结果

## 开发说明

1. 新增接口：
   - 在 handler 中添加处理函数
   - 在 service 中添加业务逻辑
   - 在 repo 中添加数据访问方法
   - 在 main.go 中注册路由

2. 数据库变更：
   - 在 model 中修改结构
   - 重启服务，自动迁移生效

3. 修改排课算法：
   - 编辑 script/schedule.py
   - 确保输入输出格式兼容

## 注意事项

1. 数据库备份：
   - SQLite：备份 data/course_arrangement.db 文件
   - PostgreSQL：使用 pg_dump 工具备份

2. 日志监控：检查服务日志了解运行状态

3. 性能优化：
   - 适当使用索引
   - 批量操作使用事务
   - 合理使用缓存（开发环境）

## 维护和更新

1. 定期更新依赖包：
```bash
go get -u ./...
```

2. 检查安全更新：
```bash
go list -m -u all
```

## 故障排除

1. 数据库连接失败
   - SQLite：检查 data 目录权限
   - PostgreSQL：验证配置和连接信息

2. Python 脚本错误
   - 检查 Python 环境和依赖
   - 查看脚本日志输出
   - 验证输入数据格式

3. API 响应异常
   - 检查请求参数格式
   - 查看服务器日志
   - 验证数据库连接状态