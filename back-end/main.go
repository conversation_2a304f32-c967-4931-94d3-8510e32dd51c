package main

import (
	"fmt"
	"log"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"github.com/txzeyi/course-arrangement/internal/handler"
	"github.com/txzeyi/course-arrangement/internal/model"
	"github.com/txzeyi/course-arrangement/internal/repo"
	"github.com/txzeyi/course-arrangement/internal/service"
)

func initConfig() {
	// 从环境变量获取环境名，默认为 local
	env := os.Getenv("ENV")
	if env == "" {
		env = "local"
	}

	viper.SetConfigName("config." + env)
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./conf")

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("Error reading config file: %s", err)
	}

	log.Printf("Using configuration for environment: %s", env)
}

func initDB() *gorm.DB {
	var db *gorm.DB
	var err error

	driver := viper.GetString("database.driver")
	switch driver {
	case "sqlite":
		dsn := viper.GetString("database.dsn")
		// 确保数据目录存在
		if err := os.MkdirAll("./data", 0755); err != nil {
			log.Fatalf("Failed to create data directory: %s", err)
		}
		db, err = gorm.Open(sqlite.Open(dsn), &gorm.Config{})
		if err != nil {
			log.Fatalf("Failed to connect to SQLite database: %s", err)
		}
		log.Println("Connected to SQLite database")

	case "postgres", "":
		// 默认使用 PostgreSQL
		dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			viper.GetString("database.host"),
			viper.GetInt("database.port"),
			viper.GetString("database.user"),
			viper.GetString("database.password"),
			viper.GetString("database.dbname"),
			viper.GetString("database.sslmode"),
		)
		db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
		if err != nil {
			log.Fatalf("Failed to connect to PostgreSQL database: %s", err)
		}
		log.Println("Connected to PostgreSQL database")

	default:
		log.Fatalf("Unsupported database driver: %s", driver)
	}

	// 自动迁移数据库表结构
	if err := model.AutoMigrate(db); err != nil {
		log.Fatalf("Failed to migrate database: %s", err)
	}

	return db
}

func setupRouter(h *handler.Handler) *gin.Engine {
	if viper.GetString("server.mode") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.Default()

	// CORS 配置
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Authorization"}
	r.Use(cors.New(config))

	// API 路由组
	api := r.Group("/api")
	{
		// 教师相关
		api.POST("/teachers", h.CreateTeacher)
		api.GET("/teachers", h.ListTeachers)

		// 学生相关
		api.POST("/students", h.CreateStudent)
		api.GET("/students", h.ListStudents)

		// 组相关
		api.POST("/groups", h.CreateGroup)
		api.GET("/groups", h.ListGroups)

		// 排课任务相关
		tasks := api.Group("/tasks")
		{
			// 任务基本操作
			tasks.POST("", h.CreateScheduleTask)
			tasks.GET("/:taskId/status", h.GetTaskStatus)
			
			// 排课结果
			tasks.GET("/:taskId/schedules", h.GetTaskSchedules)
			tasks.GET("/:taskId/unscheduled", h.GetTaskUnscheduledCourses)
			tasks.POST("/:taskId/run", h.RunScheduling)

			// 时间偏好
			pref := tasks.Group("/:taskId/preferences")
			{
				// 教师时间偏好
				pref.POST("/teachers/:entityId", func(c *gin.Context) {
					c.Set("entityType", "teacher")
					h.AddTimePreference(c)
				})

				// 学生时间偏好
				pref.POST("/students/:entityId", func(c *gin.Context) {
					c.Set("entityType", "student")
					h.AddTimePreference(c)
				})
			}

			// 组-教师配对（课程需求）
			tasks.POST("/:taskId/group-teacher", h.AddGroupTeacher)
		}
	}

	return r
}

func main() {
	// 初始化配置
	initConfig()

	// 初始化数据库连接
	db := initDB()

	// 初始化依赖
	repo := repo.NewRepository(db)
	svc := service.NewService(repo)
	h := handler.NewHandler(svc)

	// 设置路由
	r := setupRouter(h)

	// 启动服务
	port := viper.GetString("server.port")
	log.Printf("Server starting on port %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %s", err)
	}
}